'use client'

import { useState, useEffect, useRef } from 'react'
import { signIn, getSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import Link from 'next/link'
import { useTheme } from 'next-themes'
import {
  EyeIcon,
  EyeSlashIcon,
  EnvelopeIcon,
  LockClosedIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon
} from '@heroicons/react/24/outline'

const signInSchema = z.object({
  email: z
    .string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .transform(val => val.toLowerCase().trim()),
  password: z
    .string()
    .min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
})

type SignInForm = z.infer<typeof signInSchema>

export default function SignInPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [emailHasValue, setEmailHasValue] = useState(false)
  const [passwordHasValue, setPasswordHasValue] = useState(false)
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  const emailRef = useRef<HTMLInputElement>(null)
  const passwordRef = useRef<HTMLInputElement>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<SignInForm>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      rememberMe: false,
    },
  })

  const watchedEmail = watch('email')
  const watchedPassword = watch('password')

  // Function to check if input has value (including autofilled)
  const checkInputValue = (input: HTMLInputElement | null) => {
    if (!input) return false
    return input.value.length > 0 || input.matches(':-webkit-autofill')
  }

  // Check for autofilled values periodically
  useEffect(() => {
    const checkAutofill = () => {
      if (emailRef.current) {
        setEmailHasValue(checkInputValue(emailRef.current))
      }
      if (passwordRef.current) {
        setPasswordHasValue(checkInputValue(passwordRef.current))
      }
    }

    // Check immediately and then periodically
    checkAutofill()
    const interval = setInterval(checkAutofill, 100)

    // Cleanup
    return () => clearInterval(interval)
  }, [])

  // Also check when watched values change
  useEffect(() => {
    setEmailHasValue(!!watchedEmail && watchedEmail.length > 0)
  }, [watchedEmail])

  useEffect(() => {
    setPasswordHasValue(!!watchedPassword && watchedPassword.length > 0)
  }, [watchedPassword])

  useEffect(() => {
    setMounted(true)
  }, [])

  const onSubmit = async (data: SignInForm) => {
    setIsLoading(true)
    setError(null)

    try {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(data.email)) {
        setError('Please enter a valid email address.')
        setIsLoading(false)
        return
      }

      // Attempt to sign in
      const result = await signIn('credentials', {
        email: data.email.toLowerCase().trim(),
        password: data.password,
        redirect: false,
      })

      if (result?.error) {
        // Provide specific error messages
        if (result.error === 'CredentialsSignin') {
          setError('Invalid email or password. Please check your credentials and try again.')
        } else {
          setError('Authentication failed. Please try again.')
        }
      } else if (result?.ok) {
        // Get session to check user role
        const session = await getSession()

        if (!session?.user) {
          setError('Authentication failed. Please try again.')
          return
        }

        // Redirect based on user role
        if (session.user.role === 'ADMIN') {
          router.push('/admin/dashboard')
        } else {
          // For non-admin users, redirect to a different page or show error
          setError('Access denied. Admin privileges required.')
        }
      } else {
        setError('Authentication failed. Please try again.')
      }
    } catch (error) {
      console.error('Sign-in error:', error)
      setError('An unexpected error occurred. Please try again later.')
    } finally {
      setIsLoading(false)
    }
  }

  if (!mounted) {
    return null
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 py-12 px-4 sm:px-6 lg:px-8 transition-colors duration-300">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 dark:bg-grid-slate-700/25 bg-[size:20px_20px] opacity-60" />
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/50 to-transparent dark:via-slate-900/50" />

      <div className="relative w-full max-w-md space-y-8">
        {/* Theme Toggle */}
        <div className="absolute -top-16 right-0 flex items-center space-x-2">
          <button
            onClick={() => setTheme('light')}
            className={`p-2 rounded-lg transition-colors ${
              theme === 'light'
                ? 'bg-white dark:bg-slate-800 text-blue-600 dark:text-blue-400 shadow-md'
                : 'text-slate-400 hover:text-slate-600 dark:hover:text-slate-300'
            }`}
            title="Light mode"
          >
            <SunIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => setTheme('dark')}
            className={`p-2 rounded-lg transition-colors ${
              theme === 'dark'
                ? 'bg-white dark:bg-slate-800 text-blue-600 dark:text-blue-400 shadow-md'
                : 'text-slate-400 hover:text-slate-600 dark:hover:text-slate-300'
            }`}
            title="Dark mode"
          >
            <MoonIcon className="h-5 w-5" />
          </button>
          <button
            onClick={() => setTheme('system')}
            className={`p-2 rounded-lg transition-colors ${
              theme === 'system'
                ? 'bg-white dark:bg-slate-800 text-blue-600 dark:text-blue-400 shadow-md'
                : 'text-slate-400 hover:text-slate-600 dark:hover:text-slate-300'
            }`}
            title="System mode"
          >
            <ComputerDesktopIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Login Card */}
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-slate-700/50 p-8 space-y-8">
          {/* Header */}
          <div className="text-center">
            <div className="mx-auto h-16 w-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg">
              <span className="text-2xl font-bold text-white">T</span>
            </div>
            <h2 className="text-3xl font-bold text-slate-900 dark:text-white">
              Welcome Back
            </h2>
            <p className="mt-2 text-sm text-slate-600 dark:text-slate-400">
              Sign in to your admin dashboard
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className="rounded-xl bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 p-4 animate-in slide-in-from-top-2 duration-300">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm text-red-800 dark:text-red-200">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* Form */}
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {/* Email Field */}
            <div className="space-y-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <EnvelopeIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                  {...register('email', {
                    onChange: () => {
                      if (emailRef.current) {
                        setEmailHasValue(checkInputValue(emailRef.current))
                      }
                    }
                  })}
                  ref={(e) => {
                    emailRef.current = e
                    register('email').ref(e)
                  }}
                  type="email"
                  autoComplete="email"
                  className={`block w-full pl-10 pr-3 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                    errors.email
                      ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20'
                      : 'border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700'
                  } text-slate-900 dark:text-white`}
                />
                <label
                  className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                    emailHasValue
                      ? '-top-2 text-xs bg-white dark:bg-slate-700 px-2 text-blue-600 dark:text-blue-400'
                      : 'top-3 text-sm text-slate-400'
                  }`}
                >
                  {!emailHasValue && 'Email address'}
                </label>
              </div>
              {errors.email && (
                <p className="text-sm text-red-600 dark:text-red-400 animate-in slide-in-from-top-1 duration-200">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-1">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <LockClosedIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                  {...register('password', {
                    onChange: () => {
                      if (passwordRef.current) {
                        setPasswordHasValue(checkInputValue(passwordRef.current))
                      }
                    }
                  })}
                  ref={(e) => {
                    passwordRef.current = e
                    register('password').ref(e)
                  }}
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  className={`block w-full pl-10 pr-12 py-3 border rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 ${
                    errors.password
                      ? 'border-red-300 dark:border-red-600 bg-red-50 dark:bg-red-900/20'
                      : 'border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-700'
                  } text-slate-900 dark:text-white`}
                />
                <label
                  className={`absolute left-10 transition-all duration-200 pointer-events-none ${
                    passwordHasValue
                      ? '-top-2 text-xs bg-white dark:bg-slate-700 px-2 text-blue-600 dark:text-blue-400'
                      : 'top-3 text-sm text-slate-400'
                  }`}
                >
                  {!passwordHasValue && 'Password'}
                </label>
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 transition-colors" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-600 dark:text-red-400 animate-in slide-in-from-top-1 duration-200">
                  {errors.password.message}
                </p>
              )}
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  {...register('rememberMe')}
                  id="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 dark:border-slate-600 rounded bg-white dark:bg-slate-700"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-slate-700 dark:text-slate-300">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link
                  href="/auth/forgot-password"
                  className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transform transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl"
            >
              <span className="absolute left-0 inset-y-0 flex items-center pl-3">
                {isLoading ? (
                  <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <LockClosedIcon className="h-5 w-5 text-white/70 group-hover:text-white transition-colors" />
                )}
              </span>
              {isLoading ? 'Signing in...' : 'Sign in to Dashboard'}
            </button>
          </form>

          {/* Footer Links */}
          <div className="text-center space-y-4">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-slate-200 dark:border-slate-700" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-slate-800 text-slate-500 dark:text-slate-400">
                  Need help?
                </span>
              </div>
            </div>

            <div className="flex justify-center space-x-4 text-sm">
              <Link
                href="/"
                className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200 transition-colors"
              >
                ← Back to website
              </Link>
              <span className="text-slate-300 dark:text-slate-600">|</span>
              <Link
                href="/support"
                className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-200 transition-colors"
              >
                Contact support
              </Link>
            </div>
          </div>


        </div>
      </div>
    </div>
  )
}
