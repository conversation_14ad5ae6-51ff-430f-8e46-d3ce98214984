'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { redirect } from 'next/navigation'
import { useStaticContent } from '@/lib/hooks/use-static-content'
import {
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  HomeIcon,
  PhoneIcon,
  BriefcaseIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface StaticContentItem {
  id: string
  page: string
  section: string
  contentkey: string
  content: string
  contenttype: string
  displayorder: number
}

export default function StaticContentEditor() {
  const { data: session, status } = useSession()
  const { data: staticContent, loading, error, refetch, updateContent } = useStaticContent()
  const [editingItems, setEditingItems] = useState<Set<string>>(new Set())
  const [editValues, setEditValues] = useState<Record<string, string>>({})
  const [selectedPage, setSelectedPage] = useState<string>('home')
  const [selectedSection, setSelectedSection] = useState<string>('all')
  const [saving, setSaving] = useState(false)

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user || session.user.role !== 'ADMIN') {
      redirect('/admin/login')
    }
  }, [session, status])

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading static content...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading static content: {error}</p>
          <button
            onClick={refetch}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  // Get available pages and sections
  const pages = staticContent ? Object.keys(staticContent) : []
  const sections = selectedPage !== 'all' && staticContent?.[selectedPage] 
    ? Object.keys(staticContent[selectedPage]) 
    : []

  // Get filtered content items
  const getFilteredItems = (): StaticContentItem[] => {
    if (!staticContent) return []
    
    const items: StaticContentItem[] = []
    
    Object.entries(staticContent).forEach(([page, pageData]) => {
      if (selectedPage !== 'all' && page !== selectedPage) return
      
      Object.entries(pageData).forEach(([section, sectionData]) => {
        if (selectedSection !== 'all' && section !== selectedSection) return
        
        Object.entries(sectionData).forEach(([contentkey, item]) => {
          items.push({
            id: item.id,
            page,
            section,
            contentkey,
            content: item.content,
            contenttype: item.contenttype,
            displayorder: item.displayorder
          })
        })
      })
    })
    
    return items.sort((a, b) => {
      if (a.page !== b.page) return a.page.localeCompare(b.page)
      if (a.section !== b.section) return a.section.localeCompare(b.section)
      return a.displayorder - b.displayorder
    })
  }

  const handleEdit = (item: StaticContentItem) => {
    const key = `${item.page}-${item.section}-${item.contentkey}`
    setEditingItems(prev => new Set([...prev, key]))
    setEditValues(prev => ({ ...prev, [key]: item.content }))
  }

  const handleCancel = (item: StaticContentItem) => {
    const key = `${item.page}-${item.section}-${item.contentkey}`
    setEditingItems(prev => {
      const newSet = new Set(prev)
      newSet.delete(key)
      return newSet
    })
    setEditValues(prev => {
      const newValues = { ...prev }
      delete newValues[key]
      return newValues
    })
  }

  const handleSave = async (item: StaticContentItem) => {
    const key = `${item.page}-${item.section}-${item.contentkey}`
    const newContent = editValues[key]
    
    if (newContent === undefined) return
    
    setSaving(true)
    
    try {
      const success = await updateContent([{
        page: item.page,
        section: item.section,
        contentkey: item.contentkey,
        content: newContent,
        contenttype: item.contenttype
      }])
      
      if (success) {
        setEditingItems(prev => {
          const newSet = new Set(prev)
          newSet.delete(key)
          return newSet
        })
        setEditValues(prev => {
          const newValues = { ...prev }
          delete newValues[key]
          return newValues
        })
      }
    } catch (error) {
      console.error('Error saving content:', error)
    } finally {
      setSaving(false)
    }
  }

  const getPageIcon = (page: string) => {
    switch (page) {
      case 'home': return HomeIcon
      case 'about': return InformationCircleIcon
      case 'services': return BriefcaseIcon
      case 'contact': return PhoneIcon
      default: return DocumentTextIcon
    }
  }

  const filteredItems = getFilteredItems()

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Static Content Editor</h1>
          <p className="text-gray-600">Edit static text content across your website pages</p>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Page</label>
              <select
                value={selectedPage}
                onChange={(e) => {
                  setSelectedPage(e.target.value)
                  setSelectedSection('all')
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Pages</option>
                {pages.map(page => (
                  <option key={page} value={page}>
                    {page.charAt(0).toUpperCase() + page.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Section</label>
              <select
                value={selectedSection}
                onChange={(e) => setSelectedSection(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={selectedPage === 'all'}
              >
                <option value="all">All Sections</option>
                {sections.map(section => (
                  <option key={section} value={section}>
                    {section.charAt(0).toUpperCase() + section.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Content Items */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Content Items ({filteredItems.length})
            </h2>
          </div>
          
          <div className="divide-y divide-gray-200">
            {filteredItems.map((item) => {
              const key = `${item.page}-${item.section}-${item.contentkey}`
              const isEditing = editingItems.has(key)
              const PageIcon = getPageIcon(item.page)
              
              return (
                <div key={key} className="p-6 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <PageIcon className="h-5 w-5 text-gray-400" />
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <span className="font-medium">{item.page}</span>
                          <span>→</span>
                          <span className="font-medium">{item.section}</span>
                          <span>→</span>
                          <span className="font-medium">{item.contentkey}</span>
                        </div>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {item.contenttype}
                        </span>
                      </div>
                      
                      {isEditing ? (
                        <div className="space-y-3">
                          {item.contenttype === 'html' ? (
                            <textarea
                              value={editValues[key] || ''}
                              onChange={(e) => setEditValues(prev => ({ ...prev, [key]: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              rows={4}
                            />
                          ) : (
                            <input
                              type={item.contenttype === 'email' ? 'email' : item.contenttype === 'url' ? 'url' : 'text'}
                              value={editValues[key] || ''}
                              onChange={(e) => setEditValues(prev => ({ ...prev, [key]: e.target.value }))}
                              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                          )}
                          
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleSave(item)}
                              disabled={saving}
                              className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 disabled:opacity-50"
                            >
                              <CheckIcon className="h-4 w-4 mr-1" />
                              Save
                            </button>
                            <button
                              onClick={() => handleCancel(item)}
                              disabled={saving}
                              className="inline-flex items-center px-3 py-1.5 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 disabled:opacity-50"
                            >
                              <XMarkIcon className="h-4 w-4 mr-1" />
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <p className="text-gray-900 break-words">{item.content}</p>
                        </div>
                      )}
                    </div>
                    
                    {!isEditing && (
                      <button
                        onClick={() => handleEdit(item)}
                        className="ml-4 inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700"
                      >
                        <PencilIcon className="h-4 w-4 mr-1" />
                        Edit
                      </button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
          
          {filteredItems.length === 0 && (
            <div className="p-12 text-center">
              <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No content items found for the selected filters.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
