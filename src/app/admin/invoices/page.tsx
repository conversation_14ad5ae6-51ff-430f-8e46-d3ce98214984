'use client';

import { InvoicesManager } from '@/components/admin/invoices/invoices-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface Invoice {
  id: number
  clientId: number
  totalAmount: number
  subtotal?: number
  taxRate: number
  taxAmount: number
  status: string
  dueDate: string
  description?: string
  paidAt?: string
  createdAt: string
  updatedAt: string
  client?: {
    id: number
    companyName: string
    contactName: string
    contactEmail: string
  }
  contract?: {
    id: number
    contName: string
  }
  project?: {
    id: number
    name: string
  }
  order?: {
    id: number
    orderTitle: string
  }
  payments?: any[]
  _count?: {
    payments: number
  }
}

const invoiceConfig: CrudConfig<Invoice> = {
  title: 'Invoices',
  description: 'Manage invoices, billing, and payment tracking',
  endpoint: 'invoices', // Real API with detailed error handling

  columns: [
    {
      key: 'id',
      label: 'Invoice #',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '120px',
      hideable: false, // Always visible
      defaultVisible: true
    },
    {
      key: 'client.companyName',
      label: 'Client',
      sortable: true,
      searchable: true,
      renderType: 'text',
      width: '200px',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'totalAmount',
      label: 'Total Amount',
      sortable: true,
      searchable: false,
      renderType: 'currency',
      width: '140px',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      searchable: true,
      renderType: 'status',
      renderProps: {
        statusColors: {
          'DRAFT': 'bg-gray-100 text-gray-800',
          'SENT': 'bg-blue-100 text-blue-800',
          'PAID': 'bg-green-100 text-green-800',
          'OVERDUE': 'bg-red-100 text-red-800',
          'CANCELLED': 'bg-gray-100 text-gray-800',
          'Pending': 'bg-yellow-100 text-yellow-800',
          'Sent': 'bg-blue-100 text-blue-800',
          'Paid': 'bg-green-100 text-green-800',
          'Overdue': 'bg-red-100 text-red-800',
          'Cancelled': 'bg-gray-100 text-gray-800'
        }
      },
      width: '120px',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'dueDate',
      label: 'Due Date',
      sortable: true,
      renderType: 'date',
      width: '120px',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'paidAt',
      label: 'Paid Date',
      sortable: true,
      renderType: 'date',
      width: '120px',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'subtotal',
      label: 'Subtotal',
      sortable: true,
      renderType: 'currency',
      width: '120px',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'taxAmount',
      label: 'Tax Amount',
      sortable: true,
      renderType: 'currency',
      width: '120px',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'taxRate',
      label: 'Tax Rate',
      sortable: true,
      renderType: 'text',
      renderProps: {
        suffix: '%'
      },
      width: '100px',
      hideable: true,
      defaultVisible: false
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true,
      renderType: 'date',
      width: '120px',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'updatedAt',
      label: 'Last Updated',
      sortable: true,
      renderType: 'date',
      width: '120px',
      hideable: true,
      defaultVisible: false
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'view',
      label: 'View',
      icon: 'EyeIcon',
      variant: 'secondary',
      tooltip: 'View invoice details'
    },
    {
      action: 'edit',
      label: 'Edit',
      icon: 'PencilIcon',
      variant: 'primary',
      tooltip: 'Edit invoice'
    },
    {
      action: 'toggle-status',
      label: 'Mark Paid',
      icon: 'CheckIcon',
      variant: 'success',
      tooltip: 'Mark invoice as paid'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete invoice'
    }
  ],

  fields: [
    {
      key: 'clientId',
      label: 'Client',
      type: 'select',
      required: true,
      searchable: true,
      options: [] // Will be populated dynamically
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      searchable: true,
      placeholder: 'Invoice description or notes...',
      rows: 3
    },
    {
      key: 'subtotal',
      label: 'Subtotal',
      type: 'number',
      required: true,
      placeholder: '0.00'
    },
    {
      key: 'taxRate',
      label: 'Tax Rate (%)',
      type: 'number',
      defaultValue: 0,
      placeholder: '0.00'
    },
    {
      key: 'taxAmount',
      label: 'Tax Amount',
      type: 'number',
      placeholder: '0.00'
    },
    {
      key: 'totalAmount',
      label: 'Total Amount',
      type: 'number',
      required: true,
      placeholder: '0.00'
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      required: true,
      defaultValue: 'DRAFT',
      options: [
        { value: 'DRAFT', label: 'Draft' },
        { value: 'SENT', label: 'Sent' },
        { value: 'PAID', label: 'Paid' },
        { value: 'OVERDUE', label: 'Overdue' },
        { value: 'CANCELLED', label: 'Cancelled' }
      ]
    },
    {
      key: 'dueDate',
      label: 'Due Date',
      type: 'date',
      required: true
    },
    {
      key: 'paidAt',
      label: 'Paid Date',
      type: 'date'
    },
    {
      key: 'contractId',
      label: 'Contract',
      type: 'select',
      required: true,
      options: [] // Will be populated dynamically
    },
    {
      key: 'orderId',
      label: 'Order',
      type: 'select',
      required: true,
      options: [] // Will be populated dynamically
    },
    {
      key: 'projectId',
      label: 'Project (Optional)',
      type: 'select',
      options: [] // Will be populated dynamically
    }
  ],

  filters: [
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All Statuses' },
        { value: 'DRAFT', label: 'Draft' },
        { value: 'SENT', label: 'Sent' },
        { value: 'PAID', label: 'Paid' },
        { value: 'OVERDUE', label: 'Overdue' },
        { value: 'CANCELLED', label: 'Cancelled' }
      ]
    },
    {
      key: 'clientId',
      label: 'Client',
      type: 'select',
      options: [] // Will be populated dynamically
    },
    {
      key: 'dueDateRange',
      label: 'Due Date Range',
      type: 'daterange'
    }
  ],

  bulkActions: [
    {
      label: 'Mark as Sent',
      action: 'mark-sent',
      variant: 'primary'
    },
    {
      label: 'Mark as Paid',
      action: 'mark-paid',
      variant: 'success'
    },
    {
      label: 'Mark as Overdue',
      action: 'mark-overdue',
      variant: 'warning'
    },
    {
      label: 'Delete Selected',
      action: 'delete',
      variant: 'danger',
      requiresConfirmation: true,
      confirmationMessage: 'Are you sure you want to delete the selected invoices? This action cannot be undone.'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search invoices by ID, client, description...',
  defaultSort: { field: 'createdAt', direction: 'desc' },
  pageSize: 10,
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['id', 'client.companyName', 'totalAmount', 'status', 'dueDate', 'createdAt']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 3,
    sections: [
      {
        title: 'Basic Information',
        fields: ['clientId', 'description']
      },
      {
        title: 'Financial Details',
        fields: ['subtotal', 'taxRate', 'taxAmount', 'totalAmount']
      },
      {
        title: 'Status & Dates',
        fields: ['status', 'dueDate', 'paidAt']
      },
      {
        title: 'Related Records',
        fields: ['contractId', 'projectId', 'orderId']
      }
    ]
  }
};

export default function InvoicesPage() {
  return <InvoicesManager config={invoiceConfig} />;
}
