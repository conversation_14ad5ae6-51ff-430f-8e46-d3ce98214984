'use client'

import React, { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  CogIcon,
  UserIcon,
  BellIcon,
  ShieldCheckIcon,
  PaintBrushIcon,
  LinkIcon,
  XMarkIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  Squares2X2Icon,
  ListBulletIcon,
  CloudIcon,
  GlobeAltIcon,
  DocumentTextIcon,
  KeyIcon,
  EnvelopeIcon,
  BookmarkIcon,
} from '@heroicons/react/24/outline'

// Types
interface Setting {
  id: string
  key: string
  value: string
  description?: string
  category: string
  fieldtype: string
  options?: string
  isactive: boolean
  ispublic: boolean
  createdat: string
  updatedat?: string
}

interface SettingFormData {
  key: string
  value: string
  description: string
  category: string
  fieldType: string
  options: string
  isactive: boolean
  ispublic: boolean
}

interface Category {
  name: string
  label: string
  icon: any
  color: string
  description: string
}

// Enhanced category configuration with colors and descriptions
const DEFAULT_CATEGORY_CONFIG = [
  {
    name: 'GENERAL',
    label: 'General Settings',
    icon: CogIcon,
    color: 'blue',
    description: 'Basic application configuration and site-wide settings'
  },
  {
    name: 'PROFILE',
    label: 'Profile & User',
    icon: UserIcon,
    color: 'green',
    description: 'User profile settings and account preferences'
  },
  {
    name: 'NOTIFICATIONS',
    label: 'Notifications',
    icon: BellIcon,
    color: 'yellow',
    description: 'Email, SMS, and push notification settings'
  },
  {
    name: 'SECURITY',
    label: 'Security',
    icon: ShieldCheckIcon,
    color: 'red',
    description: 'Authentication, encryption, and security policies'
  },
  {
    name: 'APPEARANCE',
    label: 'Appearance',
    icon: PaintBrushIcon,
    color: 'purple',
    description: 'Theme, branding, and visual customization'
  },
  {
    name: 'INTEGRATIONS',
    label: 'Integrations',
    icon: LinkIcon,
    color: 'indigo',
    description: 'Third-party services and API configurations'
  },
  {
    name: 'SYSTEM',
    label: 'System',
    icon: CloudIcon,
    color: 'gray',
    description: 'Server settings, performance, and maintenance'
  },
  {
    name: 'CONTENT',
    label: 'Content',
    icon: DocumentTextIcon,
    color: 'emerald',
    description: 'Content management and publishing settings'
  },
]

const FIELD_TYPES = [
  { value: 'text', label: 'Text Input', width: 'max-w-md' },
  { value: 'textarea', label: 'Text Area', width: 'max-w-lg' },
  { value: 'number', label: 'Number', width: 'w-32' },
  { value: 'email', label: 'Email', width: 'max-w-md' },
  { value: 'url', label: 'URL', width: 'max-w-lg' },
  { value: 'password', label: 'Password', width: 'max-w-sm' },
  { value: 'color', label: 'Color Picker', width: 'w-16' },
  { value: 'date', label: 'Date', width: 'w-40' },
  { value: 'time', label: 'Time', width: 'w-32' },
  { value: 'checkbox', label: 'Checkbox', width: 'w-auto' },
  { value: 'toggle', label: 'Toggle Switch', width: 'w-auto' },
  { value: 'dropdown', label: 'Dropdown', width: 'max-w-sm' },
  { value: 'radio', label: 'Radio Buttons', width: 'w-auto' },
  { value: 'file', label: 'File Upload', width: 'max-w-md' },
  { value: 'range', label: 'Range Slider', width: 'max-w-sm' },
]

export default function SettingsPage() {
  // Session and authentication
  const { data: session, status } = useSession()
  
  // Core state management
  const [settings, setSettings] = useState<Setting[]>([])
  const [categories, setCategories] = useState<string[]>([])
  const [activeCategory, setActiveCategory] = useState('GENERAL')
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [dynamicCategories, setDynamicCategories] = useState<Category[]>([])

  // UI state
  const [searchTerm, setSearchTerm] = useState('')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [showInactive, setShowInactive] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'warning'; text: string } | null>(null)

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [editingSetting, setEditingSetting] = useState<Setting | null>(null)
  const [formData, setFormData] = useState<SettingFormData>({
    key: '',
    value: '',
    description: '',
    category: 'GENERAL',
    fieldType: 'text',
    options: '',
    isactive: true,
    ispublic: true,
  })

  // Category management state
  const [isCategoryModalOpen, setIsCategoryModalOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<string | null>(null)
  const [categoryFormData, setCategoryFormData] = useState({
    name: '',
    label: '',
    description: '',
    color: 'blue'
  })
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null)

  // Fetch all settings from database
  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/settings?limit=1000')
      const result = await response.json()
      
      if (result.success && result.data) {
        setSettings(result.data)
        
        // Extract unique categories and merge with defaults
        const dbCategories = [...new Set(result.data.map((s: Setting) => s.category))]
        const allCategories = [...new Set([
          ...DEFAULT_CATEGORY_CONFIG.map(c => c.name),
          ...dynamicCategories.map(c => c.name),
          ...dbCategories
        ])]
        setCategories(allCategories)
        
        // Auto-organize after fetch
        organizeSettings(result.data)
      }
    } catch (error) {
      console.error('Error fetching settings:', error)
      showMessage('error', 'Failed to load settings')
    } finally {
      setLoading(false)
    }
  }

  // Enhanced organization function
  const organizeSettings = (settingsToOrganize: Setting[] = settings) => {
    const organized = [...settingsToOrganize].sort((a, b) => {
      // First sort by category priority
      const allCategories = [...DEFAULT_CATEGORY_CONFIG, ...dynamicCategories]
      const categoryA = allCategories.findIndex(c => c.name === a.category)
      const categoryB = allCategories.findIndex(c => c.name === b.category)
      
      if (categoryA !== categoryB) {
        // Known categories first, then alphabetical for custom categories
        if (categoryA === -1 && categoryB === -1) {
          return a.category.localeCompare(b.category)
        }
        if (categoryA === -1) return 1
        if (categoryB === -1) return -1
        return categoryA - categoryB
      }
      
      // Within category, sort by key alphabetically
      return a.key.localeCompare(b.key)
    })
    
    setSettings(organized)
  }

  // Enhanced message system
  const showMessage = (type: 'success' | 'error' | 'warning', text: string) => {
    setMessage({ type, text })
    setTimeout(() => setMessage(null), 5000)
  }

  // Save all pending changes
  const saveAllChanges = async () => {
    try {
      setSaving(true)
      showMessage('success', 'All changes saved successfully')
      setHasUnsavedChanges(false)
      await fetchSettings()
    } catch (error) {
      console.error('Error saving changes:', error)
      showMessage('error', 'Failed to save changes')
    } finally {
      setSaving(false)
    }
  }

  // Category management functions
  const openCategoryModal = (categoryName?: string) => {
    if (categoryName) {
      const config = getCategoryConfig(categoryName)
      setCategoryFormData({
        name: categoryName,
        label: config.label,
        description: config.description,
        color: config.color
      })
      setEditingCategory(categoryName)
    } else {
      setCategoryFormData({
        name: '',
        label: '',
        description: '',
        color: 'blue'
      })
      setEditingCategory(null)
    }
    setIsCategoryModalOpen(true)
  }

  const closeCategoryModal = () => {
    setIsCategoryModalOpen(false)
    setEditingCategory(null)
    setCategoryFormData({
      name: '',
      label: '',
      description: '',
      color: 'blue'
    })
  }

  const saveCategory = async () => {
    try {
      setSaving(true)

      if (!categoryFormData.name.trim() || !categoryFormData.label.trim()) {
        showMessage('error', 'Category name and label are required')
        return
      }

      const newCategoryName = categoryFormData.name.toUpperCase().replace(/\s+/g, '_')

      if (!editingCategory && categories.includes(newCategoryName)) {
        showMessage('error', 'Category already exists')
        return
      }

      if (!editingCategory) {
        // Save new category to database via API
        const response = await fetch('/api/admin/settings/categories', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(categoryFormData)
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to create category')
        }

        const result = await response.json()
        if (result.success) {
          // Add to local state
          const newCategory: Category = {
            name: result.data.name,
            label: result.data.label,
            description: result.data.description,
            color: result.data.color,
            icon: DocumentTextIcon
          }
          setDynamicCategories(prev => [...prev, newCategory])
          setCategories(prev => [...prev, result.data.name])

          // Refresh settings to include new category
          await fetchSettings()
        }
      } else {
        // Update existing category (local only for now)
        const newCategory: Category = {
          name: newCategoryName,
          label: categoryFormData.label,
          icon: DocumentTextIcon,
          color: categoryFormData.color,
          description: categoryFormData.description || `Settings for ${categoryFormData.label}`
        }

        setDynamicCategories(prev =>
          prev.map(cat => cat.name === editingCategory ? newCategory : cat)
        )
      }

      showMessage('success', `Category ${editingCategory ? 'updated' : 'created'} successfully`)
      closeCategoryModal()
    } catch (error) {
      console.error('Error saving category:', error)
      showMessage('error', 'Failed to save category')
    } finally {
      setSaving(false)
    }
  }

  const deleteCategory = async (categoryName: string) => {
    if (!confirm(`Are you sure you want to delete the category "${categoryName}"? This will affect all settings in this category.`)) return

    try {
      // Check if category has settings
      const categorySettings = settings.filter(s => s.category === categoryName)
      if (categorySettings.length > 0) {
        showMessage('error', `Cannot delete category with ${categorySettings.length} settings. Move or delete settings first.`)
        return
      }

      setCategories(prev => prev.filter(c => c !== categoryName))
      if (activeCategory === categoryName) {
        setActiveCategory('GENERAL')
      }
      showMessage('success', 'Category deleted successfully')
    } catch (error) {
      console.error('Error deleting category:', error)
      showMessage('error', 'Failed to delete category')
    }
  }

  // Save setting with enhanced feedback
  const saveSetting = async () => {
    try {
      setSaving(true)

      // Validate required fields
      if (!formData.key.trim()) {
        showMessage('error', 'Setting key is required')
        return
      }

      // Check for duplicate keys when creating or changing key
      if (!isEditMode || (editingSetting && editingSetting.key !== formData.key)) {
        const existingSetting = settings.find(s => s.key === formData.key && s.id !== editingSetting?.id)
        if (existingSetting) {
          showMessage('error', 'A setting with this key already exists')
          return
        }
      }

      const url = isEditMode && editingSetting
        ? `/api/admin/settings/${editingSetting.id}`
        : '/api/admin/settings'

      const method = isEditMode ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.text()
        console.error('API Error:', errorData)
        throw new Error(`Failed to ${isEditMode ? 'update' : 'create'} setting`)
      }

      const result = await response.json()

      if (result.success) {
        showMessage('success', `Setting ${isEditMode ? 'updated' : 'created'} successfully`)
        setHasUnsavedChanges(false)

        // Refresh and reorganize
        await fetchSettings()
        closeModal()
      }
    } catch (error) {
      console.error('Error saving setting:', error)
      showMessage('error', `Failed to ${isEditMode ? 'update' : 'create'} setting`)
    } finally {
      setSaving(false)
    }
  }

  // Delete setting with confirmation
  const deleteSetting = async (id: string, key: string) => {
    if (!confirm(`Are you sure you want to delete the setting "${key}"?`)) return
    
    try {
      const response = await fetch(`/api/admin/settings/${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        showMessage('success', 'Setting deleted successfully')
        await fetchSettings()
      } else {
        throw new Error('Failed to delete setting')
      }
    } catch (error) {
      console.error('Error deleting setting:', error)
      showMessage('error', 'Failed to delete setting')
    }
  }

  // Update setting value inline with optimistic updates
  const updateSettingValue = async (setting: Setting, newValue: string) => {
    try {
      // Optimistic update
      setSettings(prev => prev.map(s =>
        s.id === setting.id ? { ...s, value: newValue } : s
      ))
      setHasUnsavedChanges(true)

      const response = await fetch(`/api/admin/settings/${setting.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...setting,
          value: newValue,
          fieldType: setting.fieldtype,
        }),
      })

      if (!response.ok) {
        // Revert optimistic update on error
        setSettings(prev => prev.map(s =>
          s.id === setting.id ? { ...s, value: setting.value } : s
        ))
        setHasUnsavedChanges(false)
        throw new Error('Failed to update setting')
      }

      showMessage('success', 'Setting updated successfully')
      setHasUnsavedChanges(false)
    } catch (error) {
      console.error('Error updating setting:', error)
      showMessage('error', 'Failed to update setting')
    }
  }

  // Enhanced field rendering with optimal widths and professional styling
  const renderDynamicField = (setting: Setting) => {
    const { fieldtype, value, options } = setting
    const fieldConfig = FIELD_TYPES.find(ft => ft.value === fieldtype)
    const fieldWidth = fieldConfig?.width || 'max-w-md'

    const baseInputClasses = `
      mt-1 block border border-gray-300 rounded-lg shadow-sm py-2.5 px-3.5
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
      transition-all duration-200 sm:text-sm bg-white hover:border-gray-400
      ${fieldWidth}
    `.trim()

    const enhancedClasses = `
      ${baseInputClasses}
      placeholder-gray-400 text-gray-900
    `.trim()

    switch (fieldtype) {
      case 'checkbox':
        return (
          <div className="flex items-center mt-2">
            <input
              type="checkbox"
              checked={value === 'true'}
              onChange={(e) => updateSettingValue(setting, e.target.checked.toString())}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors"
            />
            <span className={`ml-3 text-sm font-medium ${
              value === 'true' ? 'text-green-700' : 'text-gray-500'
            }`}>
              {value === 'true' ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        )

      case 'toggle':
        return (
          <div className="flex items-center mt-2">
            <button
              type="button"
              onClick={() => updateSettingValue(setting, (value !== 'true').toString())}
              className={`${
                value === 'true' ? 'bg-blue-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full
              border-2 border-transparent transition-colors duration-200 ease-in-out
              focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
              hover:shadow-md`}
            >
              <span
                className={`${
                  value === 'true' ? 'translate-x-5' : 'translate-x-0'
                } pointer-events-none inline-block h-5 w-5 transform rounded-full
                bg-white shadow ring-0 transition duration-200 ease-in-out`}
              />
            </button>
            <span className={`ml-3 text-sm font-medium ${
              value === 'true' ? 'text-green-700' : 'text-gray-500'
            }`}>
              {value === 'true' ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        )

      case 'dropdown':
        const optionsList = options?.split(',').map(opt => opt.trim()).filter(opt => opt) || []
        return (
          <select
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
          >
            <option value="">Select an option</option>
            {optionsList.map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </select>
        )

      case 'textarea':
        return (
          <textarea
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            rows={3}
            className={`${enhancedClasses} resize-none`}
            placeholder="Enter value..."
          />
        )

      case 'number':
        return (
          <input
            type="number"
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
            placeholder="0"
          />
        )

      case 'email':
        return (
          <input
            type="email"
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
            placeholder="<EMAIL>"
          />
        )

      case 'url':
        return (
          <input
            type="url"
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
            placeholder="https://example.com"
          />
        )

      case 'password':
        return (
          <input
            type="password"
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
            placeholder="••••••••"
          />
        )

      case 'color':
        return (
          <div className="flex items-center space-x-3 mt-1">
            <input
              type="color"
              value={value || '#000000'}
              onChange={(e) => updateSettingValue(setting, e.target.value)}
              className="w-12 h-10 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 cursor-pointer"
            />
            <span className="text-sm text-gray-600 font-mono">{value || '#000000'}</span>
          </div>
        )

      case 'date':
        return (
          <input
            type="date"
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
          />
        )

      case 'time':
        return (
          <input
            type="time"
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
          />
        )

      case 'radio':
        const radioOptions = options?.split(',').map(opt => opt.trim()).filter(opt => opt) || []
        return (
          <div className="mt-2 space-y-2">
            {radioOptions.map((option, index) => (
              <label key={index} className="flex items-center">
                <input
                  type="radio"
                  name={`setting-${setting.id}`}
                  value={option}
                  checked={value === option}
                  onChange={(e) => updateSettingValue(setting, e.target.value)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">{option}</span>
              </label>
            ))}
          </div>
        )

      case 'file':
        return (
          <div className="mt-1">
            <input
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) {
                  updateSettingValue(setting, file.name)
                }
              }}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 transition-colors"
            />
            {value && (
              <p className="mt-1 text-xs text-gray-500">Current: {value}</p>
            )}
          </div>
        )

      case 'range':
        const min = options?.split(',')[0] || '0'
        const max = options?.split(',')[1] || '100'
        const step = options?.split(',')[2] || '1'
        return (
          <div className="mt-1">
            <input
              type="range"
              min={min}
              max={max}
              step={step}
              value={value || min}
              onChange={(e) => updateSettingValue(setting, e.target.value)}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>{min}</span>
              <span className="font-medium text-blue-600">{value || min}</span>
              <span>{max}</span>
            </div>
          </div>
        )

      default:
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => updateSettingValue(setting, e.target.value)}
            className={enhancedClasses}
            placeholder="Enter value..."
          />
        )
    }
  }

  // Get category configuration with fallback for custom categories
  const getCategoryConfig = (categoryName: string) => {
    const allCategories = [...DEFAULT_CATEGORY_CONFIG, ...dynamicCategories]
    return allCategories.find(c => c.name === categoryName) || {
      name: categoryName,
      label: categoryName.charAt(0) + categoryName.slice(1).toLowerCase(),
      icon: DocumentTextIcon,
      color: 'gray',
      description: 'Custom category settings'
    }
  }

  // Get color classes for category
  const getCategoryColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string; border: string; hover: string }> = {
      blue: { bg: 'bg-blue-50', text: 'text-blue-700', border: 'border-blue-200', hover: 'hover:bg-blue-100' },
      green: { bg: 'bg-green-50', text: 'text-green-700', border: 'border-green-200', hover: 'hover:bg-green-100' },
      yellow: { bg: 'bg-yellow-50', text: 'text-yellow-700', border: 'border-yellow-200', hover: 'hover:bg-yellow-100' },
      red: { bg: 'bg-red-50', text: 'text-red-700', border: 'border-red-200', hover: 'hover:bg-red-100' },
      purple: { bg: 'bg-purple-50', text: 'text-purple-700', border: 'border-purple-200', hover: 'hover:bg-purple-100' },
      indigo: { bg: 'bg-indigo-50', text: 'text-indigo-700', border: 'border-indigo-200', hover: 'hover:bg-indigo-100' },
      emerald: { bg: 'bg-emerald-50', text: 'text-emerald-700', border: 'border-emerald-200', hover: 'hover:bg-emerald-100' },
      gray: { bg: 'bg-gray-50', text: 'text-gray-700', border: 'border-gray-200', hover: 'hover:bg-gray-100' },
      pink: { bg: 'bg-pink-50', text: 'text-pink-700', border: 'border-pink-200', hover: 'hover:bg-pink-100' },
    }
    return colorMap[color] || colorMap.gray
  }

  // Filter and search settings
  const getFilteredSettings = () => {
    let filtered = settings.filter(s => s.category === activeCategory)

    if (!showInactive) {
      filtered = filtered.filter(s => s.isactive)
    }

    if (searchTerm) {
      filtered = filtered.filter(s =>
        s.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
        s.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return filtered
  }

  // Modal functions
  const openModal = (category?: string) => {
    setFormData({
      key: '',
      value: '',
      description: '',
      category: category || activeCategory,
      fieldType: 'text',
      options: '',
      isactive: true,
      ispublic: true,
    })
    setIsEditMode(false)
    setEditingSetting(null)
    setIsModalOpen(true)
  }

  const openEditModal = (setting: Setting) => {
    setFormData({
      key: setting.key,
      value: setting.value,
      description: setting.description || '',
      category: setting.category,
      fieldType: setting.fieldtype,
      options: setting.options || '',
      isactive: setting.isactive,
      ispublic: setting.ispublic,
    })
    setIsEditMode(true)
    setEditingSetting(setting)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setIsEditMode(false)
    setEditingSetting(null)
  }

  // Load settings on mount
  useEffect(() => {
    fetchSettings()
  }, [])

  // Auto-hide messages
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 5000)
      return () => clearTimeout(timer)
    }
  }, [message])

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading settings...</p>
        </div>
      </div>
    )
  }

  const filteredSettings = getFilteredSettings()
  const activeCategoryConfig = getCategoryConfig(activeCategory)
  const categoryColors = getCategoryColorClasses(activeCategoryConfig.color)

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Enhanced Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <CogIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Settings Management</h1>
                  <p className="text-sm text-gray-500">Configure your application settings</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              {hasUnsavedChanges && (
                <button
                  onClick={saveAllChanges}
                  disabled={saving}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 disabled:opacity-50"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <BookmarkIcon className="h-4 w-4 mr-2" />
                      Save Changes
                    </>
                  )}
                </button>
              )}
              <button
                onClick={() => openModal()}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Setting
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Message Display */}
      {message && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <div className={`rounded-lg p-4 ${
            message.type === 'success' ? 'bg-green-50 border border-green-200' :
            message.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' :
            'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {message.type === 'success' ? (
                  <CheckIcon className="h-5 w-5 text-green-400" />
                ) : message.type === 'warning' ? (
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                ) : (
                  <XMarkIcon className="h-5 w-5 text-red-400" />
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm font-medium ${
                  message.type === 'success' ? 'text-green-800' :
                  message.type === 'warning' ? 'text-yellow-800' :
                  'text-red-800'
                }`}>
                  {message.text}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Compact Category Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              <div className="p-4 border-b border-gray-200 flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Categories</h3>
                  <p className="text-sm text-gray-500">Organize settings</p>
                </div>
                <button
                  onClick={() => openCategoryModal()}
                  className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200"
                  title="Add new category"
                >
                  <PlusIcon className="h-5 w-5" />
                </button>
              </div>

              <nav className="p-2">
                {categories.map((categoryName) => {
                  const config = getCategoryConfig(categoryName)
                  const Icon = config.icon
                  const colors = getCategoryColorClasses(config.color)
                  const isActive = activeCategory === categoryName
                  const settingsCount = settings.filter(s => s.category === categoryName).length
                  const isHovered = hoveredCategory === categoryName

                  return (
                    <div
                      key={categoryName}
                      className="relative group"
                      onMouseEnter={() => setHoveredCategory(categoryName)}
                      onMouseLeave={() => setHoveredCategory(null)}
                    >
                      <button
                        onClick={() => setActiveCategory(categoryName)}
                        className={`${
                          isActive
                            ? `${colors.bg} ${colors.text} ${colors.border} border-l-4`
                            : 'text-gray-600 hover:bg-gray-50 border-l-4 border-transparent hover:border-gray-200'
                        } w-full flex items-center justify-between px-3 py-2 text-sm font-medium transition-all duration-200 rounded-lg mx-1 my-0.5 ${colors.hover}`}
                      >
                        <div className="flex items-center min-w-0">
                          <Icon className="h-4 w-4 mr-2 flex-shrink-0" />
                          <span className="truncate">{config.label}</span>
                        </div>
                        <span className={`${
                          isActive ? colors.text : 'text-gray-400'
                        } inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-white shadow-sm`}>
                          {settingsCount}
                        </span>
                      </button>

                      {/* Hover Edit/Delete Icons */}
                      {isHovered && !isActive && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1 bg-white rounded-lg shadow-sm border border-gray-200 px-1">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              openCategoryModal(categoryName)
                            }}
                            className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                            title="Edit category"
                          >
                            <PencilIcon className="h-3 w-3" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteCategory(categoryName)
                            }}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete category"
                          >
                            <TrashIcon className="h-3 w-3" />
                          </button>
                        </div>
                      )}
                    </div>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Enhanced Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Category Header */}
              <div className={`${categoryColors.bg} ${categoryColors.border} border-b px-6 py-4`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 ${categoryColors.text} bg-white rounded-lg shadow-sm`}>
                      <activeCategoryConfig.icon className="h-6 w-6" />
                    </div>
                    <div>
                      <h2 className={`text-xl font-bold ${categoryColors.text}`}>
                        {activeCategoryConfig.label}
                      </h2>
                      <p className={`text-sm ${categoryColors.text} opacity-75`}>
                        {activeCategoryConfig.description}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${categoryColors.text} bg-white shadow-sm`}>
                      {filteredSettings.length} settings
                    </span>
                  </div>
                </div>
              </div>

              {/* Enhanced Controls */}
              <div className="p-6 border-b border-gray-200 bg-gray-50">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                  <div className="flex items-center space-x-3">
                    {/* Search */}
                    <div className="relative">
                      <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search settings..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm w-64"
                      />
                    </div>

                    {/* Show Inactive Toggle */}
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={showInactive}
                        onChange={(e) => setShowInactive(e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">Show inactive</span>
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* View Mode Toggle */}
                    <div className="flex items-center bg-white border border-gray-300 rounded-lg p-1">
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`p-1.5 rounded ${
                          viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                        } transition-colors`}
                      >
                        <Squares2X2Icon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`p-1.5 rounded ${
                          viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-400 hover:text-gray-600'
                        } transition-colors`}
                      >
                        <ListBulletIcon className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Save Button */}
                    {hasUnsavedChanges && (
                      <button
                        onClick={saveAllChanges}
                        disabled={saving}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 disabled:opacity-50"
                      >
                        {saving ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Saving...
                          </>
                        ) : (
                          <>
                            <BookmarkIcon className="h-4 w-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </button>
                    )}

                    {/* Save All Button */}
                    <button
                      onClick={saveAllChanges}
                      disabled={saving}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 disabled:opacity-50"
                    >
                      {saving ? (
                        <>
                          <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600 mr-1"></div>
                          Saving...
                        </>
                      ) : (
                        <>
                          <BookmarkIcon className="h-4 w-4 mr-1" />
                          Save
                        </>
                      )}
                    </button>

                    {/* Add Setting Button */}
                    <button
                      onClick={() => openModal(activeCategory)}
                      className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add
                    </button>
                  </div>
                </div>
              </div>

              {/* Compact Settings Content */}
              <div className="p-4">
                {filteredSettings.length === 0 ? (
                  <div className="text-center py-8">
                    <div className={`mx-auto h-12 w-12 ${categoryColors.bg} rounded-full flex items-center justify-center`}>
                      <activeCategoryConfig.icon className={`h-6 w-6 ${categoryColors.text}`} />
                    </div>
                    <h3 className="mt-3 text-base font-medium text-gray-900">
                      {searchTerm ? 'No matching settings' : 'No settings found'}
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {searchTerm
                        ? `No settings match "${searchTerm}" in this category.`
                        : `Create your first setting for ${activeCategoryConfig.label}.`
                      }
                    </p>
                    {!searchTerm && (
                      <div className="mt-4">
                        <button
                          onClick={() => openModal(activeCategory)}
                          className="inline-flex items-center px-3 py-2 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                        >
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Create First Setting
                        </button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className={`${
                    viewMode === 'grid'
                      ? 'grid grid-cols-1 md:grid-cols-2 gap-2'
                      : 'space-y-1'
                  }`}>
                    {filteredSettings.map((setting) => (
                      <div
                        key={setting.id}
                        className={`${
                          viewMode === 'grid'
                            ? 'bg-gray-50 border border-gray-200 rounded-lg p-2 hover:shadow-md transition-all duration-200'
                            : 'bg-white border border-gray-200 rounded-lg p-2 hover:shadow-sm transition-all duration-200'
                        } ${!setting.isactive ? 'opacity-60' : ''}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-1 mb-1">
                              <h4 className="text-xs font-semibold text-gray-900 truncate">
                                {setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </h4>
                              {!setting.isactive && (
                                <span className="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                  Inactive
                                </span>
                              )}
                              {!setting.ispublic && (
                                <span className="inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-600">
                                  Private
                                </span>
                              )}
                            </div>

                            {renderDynamicField(setting)}

                            {setting.description && (
                              <p className="text-xs text-gray-500 mt-1 leading-tight">
                                {setting.description}
                              </p>
                            )}
                          </div>

                          <div className="flex items-center space-x-0.5 ml-2">
                            <button
                              onClick={() => openEditModal(setting)}
                              className="p-1 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-all duration-200"
                              title="Edit setting"
                            >
                              <PencilIcon className="h-3 w-3" />
                            </button>
                            <button
                              onClick={() => deleteSetting(setting.id, setting.key)}
                              className="p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200"
                              title="Delete setting"
                            >
                              <TrashIcon className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="relative bg-white rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 ${categoryColors.bg} rounded-lg`}>
                    <activeCategoryConfig.icon className={`h-6 w-6 ${categoryColors.text}`} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      {isEditMode ? 'Edit Setting' : 'Create New Setting'}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {isEditMode ? 'Update setting configuration' : `Add a new setting to ${activeCategoryConfig.label}`}
                    </p>
                  </div>
                </div>
                <button
                  onClick={closeModal}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <form onSubmit={(e) => { e.preventDefault(); saveSetting(); }} className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Setting Key *
                  </label>
                  <input
                    type="text"
                    value={formData.key}
                    onChange={(e) => setFormData({ ...formData, key: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder="e.g., site_name, max_upload_size"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Unique identifier for this setting (can be changed when editing)
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    required
                  >
                    {categories.map((cat) => {
                      const config = getCategoryConfig(cat)
                      return (
                        <option key={cat} value={cat}>
                          {config.label}
                        </option>
                      )
                    })}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Field Type *
                  </label>
                  <select
                    value={formData.fieldType}
                    onChange={(e) => setFormData({ ...formData, fieldType: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    required
                  >
                    {FIELD_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Initial Value
                  </label>
                  <input
                    type="text"
                    value={formData.value}
                    onChange={(e) => setFormData({ ...formData, value: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder="Enter initial value"
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm resize-none"
                  placeholder="Describe what this setting controls and how it affects the application"
                />
              </div>

              {/* Options for fields that need choices */}
              {(['dropdown', 'radio', 'range', 'toggle'].includes(formData.fieldType)) && (
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    {formData.fieldType === 'range'
                      ? 'Range Options (min,max,step)'
                      : formData.fieldType === 'toggle'
                      ? 'Toggle Labels (off,on)'
                      : 'Choice Options (comma-separated)'}
                  </label>
                  <input
                    type="text"
                    value={formData.options}
                    onChange={(e) => setFormData({ ...formData, options: e.target.value })}
                    className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    placeholder={
                      formData.fieldType === 'range'
                        ? "0,100,1"
                        : formData.fieldType === 'toggle'
                        ? "Off, On"
                        : "Option 1, Option 2, Option 3"
                    }
                    required={['dropdown', 'radio', 'range'].includes(formData.fieldType)}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    {formData.fieldType === 'range'
                      ? 'Format: minimum,maximum,step (e.g., 0,100,1)'
                      : formData.fieldType === 'toggle'
                      ? 'Labels for off and on states (optional)'
                      : 'Separate each choice option with a comma. These will be the available selections.'}
                  </p>
                </div>
              )}

              {/* Status Options */}
              <div className="grid grid-cols-2 gap-6">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="text-sm font-semibold text-gray-900">Active Status</label>
                    <p className="text-xs text-gray-500">Enable or disable this setting</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.isactive}
                      onChange={(e) => setFormData({ ...formData, isactive: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <label className="text-sm font-semibold text-gray-900">Public Access</label>
                    <p className="text-xs text-gray-500">Allow public API access</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.ispublic}
                      onChange={(e) => setFormData({ ...formData, ispublic: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>

              {/* Modal Footer */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={closeModal}
                  className="px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-6 py-3 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      {isEditMode ? 'Update Setting' : 'Create Setting'}
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Category Management Modal */}
      {isCategoryModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-auto">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  {editingCategory ? 'Edit Category' : 'Add New Category'}
                </h3>
                <button
                  onClick={closeCategoryModal}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            <div className="px-6 py-4 space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Category Name *
                </label>
                <input
                  type="text"
                  value={categoryFormData.name}
                  onChange={(e) => setCategoryFormData({ ...categoryFormData, name: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  placeholder="e.g., CUSTOM_SETTINGS"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  Internal name for the category (uppercase, underscores) - can be edited
                </p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Display Label *
                </label>
                <input
                  type="text"
                  value={categoryFormData.label}
                  onChange={(e) => setCategoryFormData({ ...categoryFormData, label: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  placeholder="e.g., Custom Settings"
                  required
                />
                <p className="text-xs text-gray-500 mt-1">
                  User-friendly name displayed in the interface
                </p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Description
                </label>
                <textarea
                  value={categoryFormData.description}
                  onChange={(e) => setCategoryFormData({ ...categoryFormData, description: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  placeholder="Brief description of this category..."
                  rows={3}
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-900 mb-2">
                  Color Theme
                </label>
                <select
                  value={categoryFormData.color}
                  onChange={(e) => setCategoryFormData({ ...categoryFormData, color: e.target.value })}
                  className="w-full border border-gray-300 rounded-lg shadow-sm py-3 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                >
                  <option value="blue">Blue</option>
                  <option value="green">Green</option>
                  <option value="purple">Purple</option>
                  <option value="red">Red</option>
                  <option value="yellow">Yellow</option>
                  <option value="indigo">Indigo</option>
                  <option value="pink">Pink</option>
                  <option value="gray">Gray</option>
                </select>
              </div>

              {/* Live Preview */}
              {(categoryFormData.label || categoryFormData.name) && (
                <div>
                  <label className="block text-sm font-semibold text-gray-900 mb-2">
                    Preview
                  </label>
                  <div className="border border-gray-200 rounded-lg p-3 bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className={`p-1.5 ${getCategoryColorClasses(categoryFormData.color).text} bg-white rounded-lg shadow-sm`}>
                        <DocumentTextIcon className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {categoryFormData.label || categoryFormData.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          {categoryFormData.description || `Settings for ${categoryFormData.label || categoryFormData.name}`}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                onClick={closeCategoryModal}
                className="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={saveCategory}
                disabled={saving || !categoryFormData.name.trim() || !categoryFormData.label.trim()}
                className="px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                    Saving...
                  </>
                ) : (
                  editingCategory ? 'Update Category' : 'Create Category'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
