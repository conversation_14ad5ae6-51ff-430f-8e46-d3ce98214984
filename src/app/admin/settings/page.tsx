'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CogIcon,
  BellIcon,
  ShieldCheckIcon,
  UserIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  KeyIcon,
  PaintBrushIcon,
  CloudIcon,
  DocumentTextIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  XMarkIcon,
  CheckIcon,
} from '@heroicons/react/24/outline';

const defaultSections = [
  {
    id: 'general',
    name: 'General',
    icon: CogIcon,
    description: 'Basic site settings and configuration',
    category: 'GENERAL',
    order: 1,
  },
  {
    id: 'profile',
    name: 'Profile',
    icon: UserIcon,
    description: 'Your personal account settings',
    category: 'PROFILE',
    order: 2,
  },
  {
    id: 'notifications',
    name: 'Notifications',
    icon: BellIcon,
    description: 'Email and push notification preferences',
    category: 'NOTIFICATIONS',
    order: 3,
  },
  {
    id: 'security',
    name: 'Security',
    icon: ShieldCheckIcon,
    description: 'Password and security settings',
    category: 'SECURITY',
    order: 4,
  },
  {
    id: 'appearance',
    name: 'Appearance',
    icon: PaintBrushIcon,
    description: 'Theme and display preferences',
    category: 'APPEARANCE',
    order: 5,
  },
  {
    id: 'integrations',
    name: 'Integrations',
    icon: CloudIcon,
    description: 'Third-party services and APIs',
    category: 'INTEGRATIONS',
    order: 6,
  },
  {
    id: 'manage',
    name: 'Manage Settings',
    icon: DocumentTextIcon,
    description: 'Add, edit, and delete settings',
    category: 'MANAGE',
    order: 999,
  },
];

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState('general');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  // Settings management state
  const [allSettings, setAllSettings] = useState<any[]>([]);
  const [showSettingModal, setShowSettingModal] = useState(false);
  const [editingSetting, setEditingSetting] = useState<any>(null);
  const [settingForm, setSettingForm] = useState({
    key: '',
    value: '',
    description: '',
    category: 'GENERAL',
    isactive: true,
    ispublic: false,
    fieldType: 'text',
    options: ''
  });

  // Category management state
  const [customCategories, setCustomCategories] = useState<any[]>([]);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [categoryForm, setCategoryForm] = useState({
    name: '',
    description: '',
    icon: 'CogIcon',
    order: 0,
  });
  const [dynamicSections, setDynamicSections] = useState<any[]>([]);
  const [settings, setSettings] = useState({
    // General Settings
    siteName: 'Technoloway',
    siteDescription: 'Leading software development company',
    siteUrl: 'https://technoloway.com',
    contactEmail: '<EMAIL>',

    // Profile Settings
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+****************',
    timezone: 'America/New_York',

    // Notification Settings
    emailNotifications: true,
    pushNotifications: true,
    marketingEmails: false,
    weeklyReports: true,

    // Security Settings
    twoFactorAuth: false,
    sessionTimeout: 30,

    // Appearance Settings
    theme: 'light',
    language: 'en',

    // Integration Settings
    googleAnalytics: '',
    mailchimpApi: '',
    slackWebhook: '',
  });

  // Fetch settings from database
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/settings?limit=100');

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const result = await response.json();

      if (result.success && result.data) {
        // Store all settings for management
        setAllSettings(result.data);

        // Convert database settings to form state
        const dbSettings: any = {};
        result.data.forEach((setting: any) => {
          switch (setting.key) {
            // General Settings
            case 'site_name':
              dbSettings.siteName = setting.value;
              break;
            case 'site_description':
              dbSettings.siteDescription = setting.value;
              break;
            case 'site_url':
              dbSettings.siteUrl = setting.value;
              break;
            case 'contact_email':
              dbSettings.contactEmail = setting.value;
              break;

            // Profile Settings
            case 'admin_first_name':
              dbSettings.firstName = setting.value;
              break;
            case 'admin_last_name':
              dbSettings.lastName = setting.value;
              break;
            case 'admin_email':
              dbSettings.email = setting.value;
              break;
            case 'admin_phone':
              dbSettings.phone = setting.value;
              break;
            case 'admin_timezone':
              dbSettings.timezone = setting.value;
              break;

            // Notification Settings
            case 'email_notifications':
              dbSettings.emailNotifications = setting.value === 'true';
              break;
            case 'push_notifications':
              dbSettings.pushNotifications = setting.value === 'true';
              break;
            case 'marketing_emails':
              dbSettings.marketingEmails = setting.value === 'true';
              break;
            case 'weekly_reports':
              dbSettings.weeklyReports = setting.value === 'true';
              break;

            // Security Settings
            case 'two_factor_auth':
              dbSettings.twoFactorAuth = setting.value === 'true';
              break;
            case 'session_timeout':
              dbSettings.sessionTimeout = parseInt(setting.value) || 30;
              break;

            // Appearance Settings
            case 'theme':
              dbSettings.theme = setting.value;
              break;
            case 'language':
              dbSettings.language = setting.value;
              break;

            // Integration Settings
            case 'google_analytics':
              dbSettings.googleAnalytics = setting.value;
              break;
            case 'mailchimp_api':
              dbSettings.mailchimpApi = setting.value;
              break;
            case 'slack_webhook':
              dbSettings.slackWebhook = setting.value;
              break;

            default:
              break;
          }
        });

        setSettings(prev => ({
          ...prev,
          ...dbSettings
        }));
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      setMessage({ type: 'error', text: 'Failed to load settings' });
    } finally {
      setLoading(false);
    }
  };

  // Save settings to database
  const saveSettings = async () => {
    try {
      setSaving(true);
      setMessage(null);

      // Map form settings to database format - only include settings with values
      const settingsToUpdate = [
        // General Settings
        { key: 'site_name', value: settings.siteName || '', category: 'GENERAL', description: 'Website name', isactive: true },
        { key: 'site_description', value: settings.siteDescription || '', category: 'GENERAL', description: 'Website description', isactive: true },
        { key: 'site_url', value: settings.siteUrl || '', category: 'GENERAL', description: 'Website URL', isactive: true },
        { key: 'contact_email', value: settings.contactEmail || '', category: 'GENERAL', description: 'Main contact email', isactive: true },

        // Profile Settings
        { key: 'admin_first_name', value: settings.firstName || '', category: 'PROFILE', description: 'Admin first name', isactive: true },
        { key: 'admin_last_name', value: settings.lastName || '', category: 'PROFILE', description: 'Admin last name', isactive: true },
        { key: 'admin_email', value: settings.email || '', category: 'PROFILE', description: 'Admin email address', isactive: true },
        { key: 'admin_phone', value: settings.phone || '', category: 'PROFILE', description: 'Admin phone number', isactive: true },
        { key: 'admin_timezone', value: settings.timezone || 'America/New_York', category: 'PROFILE', description: 'Admin timezone', isactive: true },

        // Notification Settings
        { key: 'email_notifications', value: settings.emailNotifications.toString(), category: 'NOTIFICATIONS', description: 'Enable email notifications', isactive: true },
        { key: 'push_notifications', value: settings.pushNotifications.toString(), category: 'NOTIFICATIONS', description: 'Enable push notifications', isactive: true },
        { key: 'marketing_emails', value: settings.marketingEmails.toString(), category: 'NOTIFICATIONS', description: 'Enable marketing emails', isactive: true },
        { key: 'weekly_reports', value: settings.weeklyReports.toString(), category: 'NOTIFICATIONS', description: 'Enable weekly reports', isactive: true },

        // Security Settings
        { key: 'two_factor_auth', value: settings.twoFactorAuth.toString(), category: 'SECURITY', description: 'Enable two-factor authentication', isactive: true },
        { key: 'session_timeout', value: settings.sessionTimeout.toString(), category: 'SECURITY', description: 'Session timeout in minutes', isactive: true },

        // Appearance Settings
        { key: 'theme', value: settings.theme || 'light', category: 'APPEARANCE', description: 'Application theme', isactive: true },
        { key: 'language', value: settings.language || 'en', category: 'APPEARANCE', description: 'Application language', isactive: true },

        // Integration Settings (only save if they have values)
        ...(settings.googleAnalytics ? [{ key: 'google_analytics', value: settings.googleAnalytics, category: 'INTEGRATIONS', description: 'Google Analytics tracking ID', isactive: true }] : []),
        ...(settings.mailchimpApi ? [{ key: 'mailchimp_api', value: settings.mailchimpApi, category: 'INTEGRATIONS', description: 'Mailchimp API key', isactive: true }] : []),
        ...(settings.slackWebhook ? [{ key: 'slack_webhook', value: settings.slackWebhook, category: 'INTEGRATIONS', description: 'Slack webhook URL', isactive: true }] : []),
      ];

      // Get all existing settings first
      const allSettingsResponse = await fetch('/api/admin/settings?limit=100');
      const allSettingsResult = await allSettingsResponse.json();

      if (!allSettingsResponse.ok) {
        throw new Error('Failed to fetch existing settings');
      }

      const existingSettings = allSettingsResult.success ? allSettingsResult.data : [];

      // Update each setting
      console.log('Saving settings:', settingsToUpdate.length, 'settings to process');

      for (const setting of settingsToUpdate) {
        // Find existing setting by key
        const existingSetting = existingSettings.find((s: any) => s.key === setting.key);

        if (existingSetting) {
          // Update existing setting
          console.log('Updating existing setting:', setting.key, 'with value:', setting.value);
          const updateResponse = await fetch(`/api/admin/settings/${existingSetting.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              value: setting.value,
              description: setting.description,
            }),
          });

          if (!updateResponse.ok) {
            throw new Error(`Failed to update ${setting.key}`);
          }
        } else {
          // Create new setting
          console.log('Creating new setting:', setting.key, 'with value:', setting.value);
          const createResponse = await fetch('/api/admin/settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(setting),
          });

          if (!createResponse.ok) {
            throw new Error(`Failed to create ${setting.key}`);
          }
        }
      }

      const categoriesSaved = [...new Set(settingsToUpdate.map(s => s.category))];
      setMessage({
        type: 'success',
        text: `Settings saved successfully! Updated categories: ${categoriesSaved.join(', ')}`
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setSaving(false);
    }
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Settings management functions
  const openAddModal = (preselectedCategory?: string) => {
    setEditingSetting(null);
    setSettingForm({
      key: '',
      value: '',
      description: '',
      category: preselectedCategory || 'GENERAL',
      datatype: 'string',
      ispublic: false,
      fieldType: 'text',
      options: ''
    });
    setShowSettingModal(true);
  };

  const openEditModal = (setting: any) => {
    setEditingSetting(setting);

    // Detect field type based on current value
    let detectedFieldType = 'text';
    const value = setting.value || '';

    if (value === 'true' || value === 'false') {
      detectedFieldType = 'checkbox';
    } else if (setting.key.includes('email')) {
      detectedFieldType = 'email';
    } else if (setting.key.includes('url') || setting.key.includes('webhook')) {
      detectedFieldType = 'url';
    } else if (setting.key.includes('password') || setting.key.includes('secret')) {
      detectedFieldType = 'password';
    } else if (setting.key.includes('color') || value.startsWith('#')) {
      detectedFieldType = 'color';
    } else if (setting.key.includes('date') || /^\d{4}-\d{2}-\d{2}/.test(value)) {
      detectedFieldType = 'date';
    } else if (setting.key.includes('time') || /^\d{2}:\d{2}/.test(value)) {
      detectedFieldType = 'time';
    } else if (setting.key.includes('timeout') || setting.key.includes('limit') || setting.key.includes('count') || /^\d+$/.test(value)) {
      detectedFieldType = 'number';
    } else if (setting.key.includes('toggle') || setting.key.includes('switch')) {
      detectedFieldType = 'toggle';
    } else if (value.includes(',') && value.split(',').length > 1) {
      detectedFieldType = 'select';
    } else if (value.length > 50) {
      detectedFieldType = 'textarea';
    }

    setSettingForm({
      key: setting.key,
      value: setting.value,
      description: setting.description || '',
      category: setting.category || 'GENERAL',
      datatype: setting.datatype || 'string',
      ispublic: setting.ispublic || false,
      fieldType: detectedFieldType,
      options: ''
    });
    setShowSettingModal(true);
  };

  const closeModal = () => {
    setShowSettingModal(false);
    setEditingSetting(null);
    setSettingForm({
      key: '',
      value: '',
      description: '',
      category: 'GENERAL',
      datatype: 'string',
      ispublic: false,
      fieldType: 'text',
      options: ''
    });
  };

  // Category management functions
  const editCategory = (categoryName: string) => {
    const category = customCategories.find(cat => cat.name === categoryName);
    if (category) {
      setCategoryForm({
        name: category.name,
        description: category.description,
        icon: category.icon,
        order: category.order
      });
      setShowCategoryModal(true);
    }
  };

  const deleteCategory = async (categoryName: string) => {
    if (confirm(`Are you sure you want to delete the "${categoryName}" category? This will also delete all settings in this category.`)) {
      try {
        // Delete all settings in this category first
        const categorySettings = allSettings.filter(setting => setting.category === categoryName);
        for (const setting of categorySettings) {
          await fetch('/api/admin/settings', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ id: setting.id.toString() })
          });
        }

        // Remove category from custom categories
        const updatedCategories = customCategories.filter(cat => cat.name !== categoryName);
        setCustomCategories(updatedCategories);
        localStorage.setItem('customCategories', JSON.stringify(updatedCategories));

        // Switch to general section if we're currently viewing the deleted category
        const deletedSection = getAllSections().find(section => section.category === categoryName);
        if (deletedSection && activeSection === deletedSection.id) {
          setActiveSection('general');
        }

        // Refresh settings
        fetchSettings();
        setMessage({ type: 'success', text: 'Category deleted successfully' });
      } catch (error) {
        console.error('Error deleting category:', error);
        setMessage({ type: 'error', text: 'Failed to delete category' });
      }
    }
  };

  const handleFormChange = (key: string, value: any) => {
    setSettingForm(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSetting = async () => {
    try {
      setSaving(true);
      setMessage(null);

      if (!settingForm.key.trim()) {
        setMessage({ type: 'error', text: 'Setting key is required' });
        return;
      }

      if (editingSetting) {
        // Update existing setting
        const updateData = {
          key: settingForm.key,
          value: settingForm.value,
          description: settingForm.description,
          category: settingForm.category,
          datatype: settingForm.datatype || 'string',
          ispublic: settingForm.ispublic || false,
          isactive: true
        };

        console.log('Updating setting with data:', updateData);

        const response = await fetch(`/api/admin/settings/${editingSetting.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData),
        });

        if (!response.ok) {
          const errorData = await response.text();
          console.error('API Error:', errorData);
          throw new Error(`Failed to update setting: ${response.status}`);
        }

        setMessage({ type: 'success', text: 'Setting updated successfully!' });
      } else {
        // Create new setting
        const settingData = {
          key: settingForm.key,
          value: settingForm.value,
          description: settingForm.description,
          category: settingForm.category,
          datatype: settingForm.datatype || 'string',
          ispublic: settingForm.ispublic || false,
          isactive: true // Always set new settings as active
        };

        console.log('Creating setting with data:', settingData);

        const response = await fetch('/api/admin/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(settingData),
        });

        if (!response.ok) {
          const errorData = await response.text();
          console.error('API Error:', errorData);
          throw new Error(`Failed to create setting: ${response.status}`);
        }

        setMessage({ type: 'success', text: 'Setting created successfully!' });
      }

      // Refresh settings
      await fetchSettings();
      closeModal();
    } catch (error) {
      console.error('Error saving setting:', error);
      setMessage({ type: 'error', text: 'Failed to save setting' });
    } finally {
      setSaving(false);
    }
  };

  const deleteSetting = async (settingId: string) => {
    if (!confirm('Are you sure you want to delete this setting?')) {
      return;
    }

    try {
      setSaving(true);
      setMessage(null);

      const response = await fetch(`/api/admin/settings/${settingId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete setting');
      }

      setMessage({ type: 'success', text: 'Setting deleted successfully!' });
      await fetchSettings();
    } catch (error) {
      console.error('Error deleting setting:', error);
      setMessage({ type: 'error', text: 'Failed to delete setting' });
    } finally {
      setSaving(false);
    }
  };

  // Get all sections (default + custom)
  const getAllSections = () => {
    const customSections = dynamicSections.map(section => ({
      ...section,
      icon: getIconComponent(section.icon),
    }));

    return [...defaultSections, ...customSections].sort((a, b) => a.order - b.order);
  };

  // Get icon component by name
  const getIconComponent = (iconName: string) => {
    const iconMap: any = {
      CogIcon,
      UserIcon,
      BellIcon,
      ShieldCheckIcon,
      PaintBrushIcon,
      CloudIcon,
      DocumentTextIcon,
      GlobeAltIcon,
      EnvelopeIcon,
      KeyIcon,
    };
    return iconMap[iconName] || CogIcon;
  };

  // Category management functions
  const openCategoryModal = () => {
    setCategoryForm({
      name: '',
      description: '',
      icon: 'CogIcon',
      order: getAllSections().length + 1,
    });
    setShowCategoryModal(true);
  };

  const closeCategoryModal = () => {
    setShowCategoryModal(false);
    setCategoryForm({
      name: '',
      description: '',
      icon: 'CogIcon',
      order: 0,
    });
  };

  const handleCategoryFormChange = (key: string, value: any) => {
    setCategoryForm(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveCategory = async () => {
    try {
      setSaving(true);
      setMessage(null);

      if (!categoryForm.name.trim()) {
        setMessage({ type: 'error', text: 'Category name is required' });
        return;
      }

      const categoryId = categoryForm.name.toLowerCase().replace(/\s+/g, '_');
      const categoryKey = categoryId.toUpperCase();

      // Create the new section
      const newSection = {
        id: categoryId,
        name: categoryForm.name,
        icon: categoryForm.icon,
        description: categoryForm.description,
        category: categoryKey,
        order: categoryForm.order,
      };

      // Add to dynamic sections
      setDynamicSections(prev => [...prev, newSection]);

      // Save category info as a setting
      const categorySettingResponse = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          key: `category_${categoryId}`,
          value: JSON.stringify(newSection),
          description: `Custom category: ${categoryForm.name}`,
          category: 'SYSTEM',
          isactive: true,
          ispublic: false,
        }),
      });

      if (!categorySettingResponse.ok) {
        throw new Error('Failed to save category');
      }

      setMessage({ type: 'success', text: 'Category created successfully!' });
      closeCategoryModal();
      await fetchSettings();
    } catch (error) {
      console.error('Error saving category:', error);
      setMessage({ type: 'error', text: 'Failed to create category' });
    } finally {
      setSaving(false);
    }
  };

  // Load custom categories from settings
  const loadCustomCategories = () => {
    const categorySettings = allSettings.filter(setting =>
      setting.key.startsWith('category_') && setting.category === 'SYSTEM'
    );

    const customSections = categorySettings.map(setting => {
      try {
        return JSON.parse(setting.value);
      } catch {
        return null;
      }
    }).filter(Boolean);

    setDynamicSections(customSections);
  };

  // Load settings and categories on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Load custom categories when settings change
  useEffect(() => {
    if (allSettings.length > 0) {
      loadCustomCategories();
    }
  }, [allSettings]);

  // Render setting field with consistent styling
  const renderSettingField = (key: string, label: string, description: string, value: any, onChange: (value: any) => void, type: 'text' | 'email' | 'url' | 'textarea' | 'select' | 'toggle' | 'number' = 'text', options?: any) => {
    const settingData = allSettings.find(s => s.key === key);

    return (
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-700">{label}</label>
          {settingData && (
            <div className="flex space-x-1">
              <button
                onClick={() => openEditModal(settingData)}
                className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                title="Edit setting"
              >
                <PencilIcon className="h-4 w-4" />
              </button>
              <button
                onClick={() => deleteSetting(settingData.id.toString())}
                className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                title="Delete setting"
              >
                <TrashIcon className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>

        {type === 'textarea' ? (
          <textarea
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            rows={3}
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        ) : type === 'select' ? (
          <select
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            {options?.map((option: any) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        ) : type === 'toggle' ? (
          <div className="flex items-center">
            <button
              type="button"
              onClick={() => onChange(!value)}
              className={`${
                value ? 'bg-blue-600' : 'bg-gray-200'
              } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
            >
              <span
                className={`${
                  value ? 'translate-x-5' : 'translate-x-0'
                } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
              />
            </button>
            <span className="ml-3 text-sm text-gray-600">
              {value ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        ) : type === 'number' ? (
          <input
            type="number"
            value={value || ''}
            onChange={(e) => onChange(parseInt(e.target.value) || 0)}
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        ) : (
          <input
            type={type}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        )}

        <p className="text-xs text-gray-500 mt-1">{description}</p>
      </div>
    );
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      {renderSettingField(
        'site_name',
        'Site Name',
        'The name of your website or application',
        settings.siteName,
        (value) => handleSettingChange('siteName', value)
      )}

      {renderSettingField(
        'site_description',
        'Site Description',
        'A brief description of your website or application',
        settings.siteDescription,
        (value) => handleSettingChange('siteDescription', value),
        'textarea'
      )}

      {renderSettingField(
        'site_url',
        'Site URL',
        'The main URL of your website',
        settings.siteUrl,
        (value) => handleSettingChange('siteUrl', value),
        'url'
      )}

      {renderSettingField(
        'contact_email',
        'Contact Email',
        'Primary contact email address for your organization',
        settings.contactEmail,
        (value) => handleSettingChange('contactEmail', value),
        'email'
      )}
    </div>
  );

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          {renderSettingField(
            'admin_first_name',
            'First Name',
            'Administrator first name',
            settings.firstName,
            (value) => handleSettingChange('firstName', value)
          )}
        </div>

        <div>
          {renderSettingField(
            'admin_last_name',
            'Last Name',
            'Administrator last name',
            settings.lastName,
            (value) => handleSettingChange('lastName', value)
          )}
        </div>
      </div>

      {renderSettingField(
        'admin_email',
        'Email',
        'Administrator email address',
        settings.email,
        (value) => handleSettingChange('email', value),
        'email'
      )}

      {renderSettingField(
        'admin_phone',
        'Phone',
        'Administrator phone number',
        settings.phone,
        (value) => handleSettingChange('phone', value)
      )}

      {renderSettingField(
        'admin_timezone',
        'Timezone',
        'Administrator timezone preference',
        settings.timezone,
        (value) => handleSettingChange('timezone', value),
        'select',
        [
          { value: 'America/New_York', label: 'Eastern Time' },
          { value: 'America/Chicago', label: 'Central Time' },
          { value: 'America/Denver', label: 'Mountain Time' },
          { value: 'America/Los_Angeles', label: 'Pacific Time' }
        ]
      )}
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {renderSettingField(
        'email_notifications',
        'Email Notifications',
        'Receive notifications via email',
        settings.emailNotifications,
        (value) => handleSettingChange('emailNotifications', value),
        'toggle'
      )}

      {renderSettingField(
        'push_notifications',
        'Push Notifications',
        'Receive push notifications in browser',
        settings.pushNotifications,
        (value) => handleSettingChange('pushNotifications', value),
        'toggle'
      )}

      {renderSettingField(
        'marketing_emails',
        'Marketing Emails',
        'Receive marketing and promotional emails',
        settings.marketingEmails,
        (value) => handleSettingChange('marketingEmails', value),
        'toggle'
      )}

      {renderSettingField(
        'weekly_reports',
        'Weekly Reports',
        'Receive weekly analytics reports',
        settings.weeklyReports,
        (value) => handleSettingChange('weeklyReports', value),
        'toggle'
      )}
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      {renderSettingField(
        'two_factor_auth',
        'Two-Factor Authentication',
        'Add an extra layer of security to your account',
        settings.twoFactorAuth,
        (value) => handleSettingChange('twoFactorAuth', value),
        'toggle'
      )}

      {renderSettingField(
        'session_timeout',
        'Session Timeout',
        'Automatically log out after period of inactivity',
        settings.sessionTimeout,
        (value) => handleSettingChange('sessionTimeout', value),
        'select',
        [
          { value: 15, label: '15 minutes' },
          { value: 30, label: '30 minutes' },
          { value: 60, label: '1 hour' },
          { value: 120, label: '2 hours' },
          { value: 480, label: '8 hours' }
        ]
      )}

      <div className="space-y-4 pt-4 border-t border-gray-200">
        <button className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Change Password
        </button>

        <button className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Download Account Data
        </button>
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      {renderSettingField(
        'theme',
        'Theme',
        'Choose your preferred color theme',
        settings.theme,
        (value) => handleSettingChange('theme', value),
        'select',
        [
          { value: 'light', label: 'Light' },
          { value: 'dark', label: 'Dark' },
          { value: 'auto', label: 'Auto (System)' }
        ]
      )}

      {renderSettingField(
        'language',
        'Language',
        'Select your preferred language',
        settings.language,
        (value) => handleSettingChange('language', value),
        'select',
        [
          { value: 'en', label: 'English' },
          { value: 'es', label: 'Spanish' },
          { value: 'fr', label: 'French' },
          { value: 'de', label: 'German' },
          { value: 'ar', label: 'Arabic' }
        ]
      )}
    </div>
  );

  const renderIntegrationSettings = () => (
    <div className="space-y-6">
      {renderSettingField(
        'google_analytics',
        'Google Analytics Tracking ID',
        'Enter your Google Analytics tracking ID to enable analytics (e.g., GA-XXXXXXXXX-X)',
        settings.googleAnalytics,
        (value) => handleSettingChange('googleAnalytics', value)
      )}

      {renderSettingField(
        'mailchimp_api',
        'Mailchimp API Key',
        'Used for email marketing and newsletter subscriptions',
        settings.mailchimpApi,
        (value) => handleSettingChange('mailchimpApi', value)
      )}

      {renderSettingField(
        'slack_webhook',
        'Slack Webhook URL',
        'Receive notifications in your Slack workspace',
        settings.slackWebhook,
        (value) => handleSettingChange('slackWebhook', value),
        'url'
      )}
    </div>
  );

  const renderManageSettings = () => (
    <div className="space-y-6">
      {/* Header with Add Button */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium text-gray-900">All Settings</h3>
          <p className="text-sm text-gray-500">Manage all application settings</p>
        </div>
        <button
          onClick={openAddModal}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Setting
        </button>
      </div>

      {/* Settings Table */}
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Key
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {allSettings.map((setting) => (
                <tr key={setting.id.toString()} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{setting.key}</div>
                    {setting.description && (
                      <div className="text-sm text-gray-500">{setting.description}</div>
                    )}
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 max-w-xs truncate">
                      {setting.value || <span className="text-gray-400 italic">Empty</span>}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      setting.category === 'GENERAL' ? 'bg-blue-100 text-blue-800' :
                      setting.category === 'PROFILE' ? 'bg-green-100 text-green-800' :
                      setting.category === 'NOTIFICATIONS' ? 'bg-yellow-100 text-yellow-800' :
                      setting.category === 'SECURITY' ? 'bg-red-100 text-red-800' :
                      setting.category === 'APPEARANCE' ? 'bg-purple-100 text-purple-800' :
                      setting.category === 'INTEGRATIONS' ? 'bg-indigo-100 text-indigo-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {setting.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      setting.isactive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {setting.isactive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => openEditModal(setting)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                        title="Edit setting"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => deleteSetting(setting.id.toString())}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        title="Delete setting"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {allSettings.length === 0 && (
          <div className="text-center py-8">
            <p className="text-gray-500">No settings found</p>
          </div>
        )}
      </div>
    </div>
  );

  // Render dynamic category section
  const renderDynamicCategorySection = (category: string) => {
    const categorySettings = allSettings.filter(setting => setting.category === category);
    const sectionInfo = getAllSections().find(section => section.category === category);

    return (
      <div className="space-y-6">
        {/* Settings List */}
        <div className="space-y-4">
          {categorySettings.map((setting) => (
            <div key={setting.id.toString()} className="space-y-1">
              <div className="flex items-center justify-between">
                <label className="block text-sm font-medium text-gray-700">
                  {setting.description || setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </label>
                <div className="flex space-x-1">
                  <button
                    onClick={() => openEditModal(setting)}
                    className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                    title="Edit setting"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => deleteSetting(setting.id.toString())}
                    className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                    title="Delete setting"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Dynamic Input Field */}
              {renderDynamicField(setting)}

              <p className="text-xs text-gray-500 mt-1">{setting.key}</p>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {categorySettings.length === 0 && (
          <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
            <CogIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No settings yet</h3>
            <p className="mt-1 text-sm text-gray-500">Get started by creating your first setting for this category.</p>
            <div className="mt-6">
              <button
                onClick={() => openAddModal(category)}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add First Setting
              </button>
            </div>
          </div>
        )}
      </div>
    );
  };

  // Render dynamic input field based on setting type
  const renderDynamicField = (setting: any) => {
    const value = setting.value || '';

    // Detect field type based on value
    if (value === 'true' || value === 'false') {
      // Boolean toggle
      return (
        <div className="flex items-center mt-1">
          <button
            type="button"
            onClick={() => updateSettingValue(setting, (!JSON.parse(value)).toString())}
            className={`${
              value === 'true' ? 'bg-blue-600' : 'bg-gray-200'
            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          >
            <span
              className={`${
                value === 'true' ? 'translate-x-5' : 'translate-x-0'
              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
            />
          </button>
          <span className="ml-3 text-sm text-gray-600">
            {value === 'true' ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      );
    } else if (setting.key.includes('email') || setting.key.includes('url') || setting.key.includes('webhook')) {
      // URL or Email input
      return (
        <input
          type={setting.key.includes('email') ? 'email' : 'url'}
          value={value}
          onChange={(e) => updateSettingValue(setting, e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder={setting.key.includes('email') ? 'Enter email address' : 'Enter URL'}
        />
      );
    } else if (setting.key.includes('timeout') || setting.key.includes('limit') || setting.key.includes('count')) {
      // Number input
      return (
        <input
          type="number"
          value={value}
          onChange={(e) => updateSettingValue(setting, e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Enter number"
        />
      );
    } else if (value.length > 50) {
      // Textarea for long content
      return (
        <textarea
          value={value}
          onChange={(e) => updateSettingValue(setting, e.target.value)}
          rows={3}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Enter value"
        />
      );
    } else {
      // Default text input
      return (
        <input
          type="text"
          value={value}
          onChange={(e) => updateSettingValue(setting, e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Enter value"
        />
      );
    }
  };

  // Update setting value
  const updateSettingValue = async (setting: any, newValue: string) => {
    try {
      const response = await fetch(`/api/admin/settings/${setting.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          value: newValue,
          description: setting.description,
          category: setting.category,
          isactive: setting.isactive,
          ispublic: setting.ispublic,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update setting');
      }

      // Update local state
      setAllSettings(prev => prev.map(s =>
        s.id === setting.id ? { ...s, value: newValue } : s
      ));

      // Also update the main settings state if it's a known setting
      const settingKey = setting.key;
      if (settings.hasOwnProperty(settingKey)) {
        setSettings(prev => ({
          ...prev,
          [settingKey]: newValue
        }));
      }

    } catch (error) {
      console.error('Error updating setting:', error);
      setMessage({ type: 'error', text: 'Failed to update setting' });
    }
  };

  // Render section header with consistent styling and Add Setting button
  const renderSectionHeader = (title: string, description: string, category: string) => (
    <div className="flex justify-between items-start pb-4 border-b border-gray-200 mb-4">
      <div>
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        <p className="text-sm text-gray-500">{description}</p>
      </div>
      <button
        onClick={() => openAddModal(category)}
        className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <PlusIcon className="h-4 w-4 mr-1" />
        Add Setting
      </button>
    </div>
  );

  const renderContent = () => {
    const section = getAllSections().find(s => s.id === activeSection);

    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'profile':
        return renderProfileSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'security':
        return renderSecuritySettings();
      case 'appearance':
        return renderAppearanceSettings();
      case 'integrations':
        return renderIntegrationSettings();
      case 'manage':
        return renderManageSettings();
      default:
        // Check if it's a custom category
        if (section && section.category) {
          return renderDynamicCategorySection(section.category);
        }
        return <div>Settings section not implemented yet.</div>;
    }
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Settings
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="lg:grid lg:grid-cols-12 lg:gap-x-5">
          {/* Settings Navigation */}
          <aside className="py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">Categories</h3>
              <button
                onClick={openCategoryModal}
                className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded"
                title="Add Category"
              >
                <PlusIcon className="h-5 w-5" />
              </button>
            </div>
            <nav className="space-y-1">
              {getAllSections().map((section) => {
                const Icon = section.icon;
                const isCustomCategory = section.category && !['general', 'profile', 'notifications', 'security', 'appearance', 'integrations', 'manage'].includes(section.id);

                return (
                  <div key={section.id} className="group relative">
                    <button
                      onClick={() => setActiveSection(section.id)}
                      className={`${
                        activeSection === section.id
                          ? 'bg-blue-50 border-blue-500 text-blue-700'
                          : 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900'
                      } group border-l-4 px-3 py-2 flex items-center text-sm font-medium w-full text-left`}
                    >
                      <Icon
                        className={`${
                          activeSection === section.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                        } flex-shrink-0 -ml-1 mr-3 h-6 w-6`}
                      />
                      <span className="truncate flex-1">{section.name}</span>
                    </button>

                    {/* Category Edit/Delete Buttons - Show on hover for custom categories */}
                    {isCustomCategory && (
                      <div className="absolute right-2 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            editCategory(section.category);
                          }}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                          title="Edit category"
                        >
                          <PencilIcon className="h-3 w-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteCategory(section.category);
                          }}
                          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                          title="Delete category"
                        >
                          <TrashIcon className="h-3 w-3" />
                        </button>
                      </div>
                    )}
                  </div>
                );
              })}
            </nav>
          </aside>

          {/* Settings Content */}
          <div className="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white shadow rounded-lg"
            >
              <div className="px-4 py-5 sm:p-6">
                <div className="flex justify-between items-start pb-4 border-b border-gray-200 mb-4">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {getAllSections().find(s => s.id === activeSection)?.name}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {getAllSections().find(s => s.id === activeSection)?.description}
                    </p>
                  </div>
                  {activeSection !== 'manage' && (
                    <button
                      onClick={() => {
                        const section = getAllSections().find(s => s.id === activeSection);
                        const category = section?.category || activeSection.toUpperCase();
                        openAddModal(category);
                      }}
                      className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add Setting
                    </button>
                  )}
                </div>
                
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  renderContent()
                )}

                {/* Message Display */}
                {message && (
                  <div className={`mt-4 p-4 rounded-md ${
                    message.type === 'success'
                      ? 'bg-green-50 border border-green-200 text-green-800'
                      : 'bg-red-50 border border-red-200 text-red-800'
                  }`}>
                    {message.text}
                  </div>
                )}

                <div className="mt-6 flex justify-end">
                  <button
                    onClick={saveSettings}
                    disabled={saving || loading}
                    className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {saving ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Category Modal */}
      {showCategoryModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Modal Header */}
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">Add New Category</h3>
                <button
                  onClick={closeCategoryModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Modal Form */}
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Category Name *
                  </label>
                  <input
                    type="text"
                    value={categoryForm.name}
                    onChange={(e) => handleCategoryFormChange('name', e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="e.g., Custom Settings"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <input
                    type="text"
                    value={categoryForm.description}
                    onChange={(e) => handleCategoryFormChange('description', e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Brief description of this category"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Icon
                  </label>
                  <select
                    value={categoryForm.icon}
                    onChange={(e) => handleCategoryFormChange('icon', e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="CogIcon">Cog (Settings)</option>
                    <option value="GlobeAltIcon">Globe (Global)</option>
                    <option value="EnvelopeIcon">Envelope (Email)</option>
                    <option value="KeyIcon">Key (Security)</option>
                    <option value="CloudIcon">Cloud (Services)</option>
                    <option value="DocumentTextIcon">Document (Content)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Display Order
                  </label>
                  <input
                    type="number"
                    value={categoryForm.order}
                    onChange={(e) => handleCategoryFormChange('order', parseInt(e.target.value) || 0)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Order in sidebar"
                  />
                </div>
              </div>

              {/* Modal Actions */}
              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <button
                  onClick={closeCategoryModal}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={saveCategory}
                  disabled={saving || !categoryForm.name.trim()}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      Create Category
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettingModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div className="mt-3">
              {/* Modal Header */}
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-medium text-gray-900">
                  {editingSetting ? 'Edit Setting' : 'Add New Setting'}
                </h3>
                <button
                  onClick={closeModal}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Modal Form */}
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Setting Key */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Setting Key *
                    </label>
                    <input
                      type="text"
                      value={settingForm.key}
                      onChange={(e) => handleFormChange('key', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="e.g., site_name"
                    />
                    <p className="mt-1 text-xs text-gray-500">Unique identifier for this setting</p>
                  </div>

                  {/* Category */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Category {activeSection !== 'manage' && '(Fixed for this section)'}
                    </label>
                    <select
                      value={settingForm.category}
                      onChange={(e) => handleFormChange('category', e.target.value)}
                      disabled={activeSection !== 'manage'}
                      className={`mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm ${
                        activeSection !== 'manage' ? 'bg-gray-100 cursor-not-allowed' : ''
                      }`}
                    >
                      <option value="GENERAL">General</option>
                      <option value="PROFILE">Profile</option>
                      <option value="NOTIFICATIONS">Notifications</option>
                      <option value="SECURITY">Security</option>
                      <option value="APPEARANCE">Appearance</option>
                      <option value="INTEGRATIONS">Integrations</option>
                      {dynamicSections.map(section => (
                        <option key={section.category} value={section.category}>
                          {section.name}
                        </option>
                      ))}
                    </select>
                    {activeSection !== 'manage' && (
                      <p className="mt-1 text-xs text-gray-500">
                        Category is automatically set for this section
                      </p>
                    )}
                  </div>
                </div>

                {/* Field Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Field Type *
                  </label>
                  <select
                    value={settingForm.fieldType}
                    onChange={(e) => handleFormChange('fieldType', e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option value="text">Text Input</option>
                    <option value="textarea">Textarea (Multi-line)</option>
                    <option value="email">Email Input</option>
                    <option value="url">URL Input</option>
                    <option value="number">Number Input</option>
                    <option value="password">Password Input</option>
                    <option value="checkbox">Checkbox (True/False)</option>
                    <option value="toggle">Toggle Switch (On/Off)</option>
                    <option value="select">Select Dropdown</option>
                    <option value="radio">Radio Buttons</option>
                    <option value="color">Color Picker</option>
                    <option value="date">Date Picker</option>
                    <option value="time">Time Picker</option>
                    <option value="file">File Upload</option>
                    <option value="range">Range Slider</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">Choose the input type for this setting</p>
                </div>

                {/* Options for Select List and Radio Buttons */}
                {['select', 'radio'].includes(settingForm.fieldType) && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {settingForm.fieldType === 'select' ? 'Select Options' : 'Radio Button Options'} *
                    </label>
                    <textarea
                      value={settingForm.options}
                      onChange={(e) => handleFormChange('options', e.target.value)}
                      rows={3}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="Enter options separated by commas: Option 1, Option 2, Option 3"
                    />
                    <p className="mt-1 text-xs text-gray-500">Separate options with commas</p>
                  </div>
                )}

                {/* Range Options */}
                {settingForm.fieldType === 'range' && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Min Value</label>
                      <input
                        type="number"
                        value={(settingForm.options || '0,100,1').split(',')[0] || '0'}
                        onChange={(e) => {
                          const parts = (settingForm.options || '0,100,1').split(',');
                          parts[0] = e.target.value;
                          handleFormChange('options', parts.join(','));
                        }}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Max Value</label>
                      <input
                        type="number"
                        value={(settingForm.options || '0,100,1').split(',')[1] || '100'}
                        onChange={(e) => {
                          const parts = (settingForm.options || '0,100,1').split(',');
                          parts[1] = e.target.value;
                          handleFormChange('options', parts.join(','));
                        }}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700">Step</label>
                      <input
                        type="number"
                        value={(settingForm.options || '0,100,1').split(',')[2] || '1'}
                        onChange={(e) => {
                          const parts = (settingForm.options || '0,100,1').split(',');
                          parts[2] = e.target.value;
                          handleFormChange('options', parts.join(','));
                        }}
                        className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="1"
                      />
                    </div>
                  </div>
                )}

                {/* Setting Value */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    {['checkbox', 'toggle'].includes(settingForm.fieldType) ? 'Default Value (true/false)' : 'Value'}
                  </label>
                  {settingForm.fieldType === 'textarea' ? (
                    <textarea
                      value={settingForm.value}
                      onChange={(e) => handleFormChange('value', e.target.value)}
                      rows={3}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="Enter setting value"
                    />
                  ) : ['checkbox', 'toggle'].includes(settingForm.fieldType) ? (
                    <select
                      value={settingForm.value}
                      onChange={(e) => handleFormChange('value', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="false">False</option>
                      <option value="true">True</option>
                    </select>
                  ) : ['select', 'radio'].includes(settingForm.fieldType) ? (
                    <select
                      value={settingForm.value}
                      onChange={(e) => handleFormChange('value', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    >
                      <option value="">Select default value...</option>
                      {(settingForm.options || '').split(',').filter(opt => opt.trim()).map((option, index) => (
                        <option key={index} value={option.trim()}>
                          {option.trim()}
                        </option>
                      ))}
                    </select>
                  ) : settingForm.fieldType === 'range' ? (
                    <div className="space-y-2">
                      {(() => {
                        const options = (settingForm.options || '0,100,1').split(',');
                        const min = options[0] || '0';
                        const max = options[1] || '100';
                        const step = options[2] || '1';
                        return (
                          <>
                            <input
                              type="range"
                              value={settingForm.value || min}
                              onChange={(e) => handleFormChange('value', e.target.value)}
                              min={min}
                              max={max}
                              step={step}
                              className="mt-1 block w-full"
                            />
                            <div className="flex justify-between text-xs text-gray-500">
                              <span>{min}</span>
                              <span>Current: {settingForm.value || min}</span>
                              <span>{max}</span>
                            </div>
                          </>
                        );
                      })()}
                    </div>
                  ) : settingForm.fieldType === 'color' ? (
                    <div className="flex space-x-2">
                      <input
                        type="color"
                        value={settingForm.value || '#000000'}
                        onChange={(e) => handleFormChange('value', e.target.value)}
                        className="mt-1 h-10 w-20 border border-gray-300 rounded-md"
                      />
                      <input
                        type="text"
                        value={settingForm.value}
                        onChange={(e) => handleFormChange('value', e.target.value)}
                        className="mt-1 flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        placeholder="#000000"
                      />
                    </div>
                  ) : (
                    <input
                      type={settingForm.fieldType === 'toggle' ? 'text' : settingForm.fieldType}
                      value={settingForm.value}
                      onChange={(e) => handleFormChange('value', e.target.value)}
                      className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder={`Enter ${settingForm.fieldType} value`}
                    />
                  )}
                  <p className="mt-1 text-xs text-gray-500">
                    {['checkbox', 'toggle'].includes(settingForm.fieldType)
                      ? 'Choose the default state for this field'
                      : settingForm.fieldType === 'range'
                      ? 'Set the default value for the range slider'
                      : settingForm.fieldType === 'color'
                      ? 'Choose the default color value'
                      : 'Initial value for this setting'
                    }
                  </p>
                </div>

                {/* Description */}
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <input
                    type="text"
                    value={settingForm.description}
                    onChange={(e) => handleFormChange('description', e.target.value)}
                    className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="Brief description of this setting"
                  />
                </div>

                {/* Status Toggles */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isactive"
                      checked={settingForm.isactive}
                      onChange={(e) => handleFormChange('isactive', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="isactive" className="ml-2 block text-sm text-gray-900">
                      Active
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="ispublic"
                      checked={settingForm.ispublic}
                      onChange={(e) => handleFormChange('ispublic', e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="ispublic" className="ml-2 block text-sm text-gray-900">
                      Public
                    </label>
                  </div>
                </div>
              </div>

              {/* Modal Actions */}
              <div className="flex justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  onClick={saveSetting}
                  disabled={saving || !settingForm.key.trim()}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {saving ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      {editingSetting ? 'Update Setting' : 'Create Setting'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
