'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CogIcon,
  BellIcon,
  ShieldCheckIcon,
  UserIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  KeyIcon,
  PaintBrushIcon,
  CloudIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

const settingsSections = [
  {
    id: 'general',
    name: 'General',
    icon: CogIcon,
    description: 'Basic site settings and configuration',
  },
  {
    id: 'profile',
    name: 'Profile',
    icon: UserIcon,
    description: 'Your personal account settings',
  },
  {
    id: 'notifications',
    name: 'Notifications',
    icon: BellIcon,
    description: 'Email and push notification preferences',
  },
  {
    id: 'security',
    name: 'Security',
    icon: ShieldCheckIcon,
    description: 'Password and security settings',
  },
  {
    id: 'appearance',
    name: 'Appearance',
    icon: PaintBrushIcon,
    description: 'Theme and display preferences',
  },
  {
    id: 'integrations',
    name: 'Integrations',
    icon: CloudIcon,
    description: 'Third-party services and APIs',
  },
];

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState('general');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [settings, setSettings] = useState({
    // General Settings
    siteName: 'Technoloway',
    siteDescription: 'Leading software development company',
    siteUrl: 'https://technoloway.com',
    contactEmail: '<EMAIL>',

    // Profile Settings
    firstName: 'Admin',
    lastName: 'User',
    email: '<EMAIL>',
    phone: '+****************',
    timezone: 'America/New_York',

    // Notification Settings
    emailNotifications: true,
    pushNotifications: true,
    marketingEmails: false,
    weeklyReports: true,

    // Security Settings
    twoFactorAuth: false,
    sessionTimeout: 30,

    // Appearance Settings
    theme: 'light',
    language: 'en',

    // Integration Settings
    googleAnalytics: '',
    mailchimpApi: '',
    slackWebhook: '',
  });

  // Fetch settings from database
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/settings?limit=100');

      if (!response.ok) {
        throw new Error('Failed to fetch settings');
      }

      const result = await response.json();

      if (result.success && result.data) {
        // Convert database settings to form state
        const dbSettings: any = {};
        result.data.forEach((setting: any) => {
          switch (setting.key) {
            // General Settings
            case 'site_name':
              dbSettings.siteName = setting.value;
              break;
            case 'site_description':
              dbSettings.siteDescription = setting.value;
              break;
            case 'site_url':
              dbSettings.siteUrl = setting.value;
              break;
            case 'contact_email':
              dbSettings.contactEmail = setting.value;
              break;

            // Profile Settings
            case 'admin_first_name':
              dbSettings.firstName = setting.value;
              break;
            case 'admin_last_name':
              dbSettings.lastName = setting.value;
              break;
            case 'admin_email':
              dbSettings.email = setting.value;
              break;
            case 'admin_phone':
              dbSettings.phone = setting.value;
              break;
            case 'admin_timezone':
              dbSettings.timezone = setting.value;
              break;

            // Notification Settings
            case 'email_notifications':
              dbSettings.emailNotifications = setting.value === 'true';
              break;
            case 'push_notifications':
              dbSettings.pushNotifications = setting.value === 'true';
              break;
            case 'marketing_emails':
              dbSettings.marketingEmails = setting.value === 'true';
              break;
            case 'weekly_reports':
              dbSettings.weeklyReports = setting.value === 'true';
              break;

            // Security Settings
            case 'two_factor_auth':
              dbSettings.twoFactorAuth = setting.value === 'true';
              break;
            case 'session_timeout':
              dbSettings.sessionTimeout = parseInt(setting.value) || 30;
              break;

            // Appearance Settings
            case 'theme':
              dbSettings.theme = setting.value;
              break;
            case 'language':
              dbSettings.language = setting.value;
              break;

            // Integration Settings
            case 'google_analytics':
              dbSettings.googleAnalytics = setting.value;
              break;
            case 'mailchimp_api':
              dbSettings.mailchimpApi = setting.value;
              break;
            case 'slack_webhook':
              dbSettings.slackWebhook = setting.value;
              break;

            default:
              break;
          }
        });

        setSettings(prev => ({
          ...prev,
          ...dbSettings
        }));
      }
    } catch (error) {
      console.error('Error fetching settings:', error);
      setMessage({ type: 'error', text: 'Failed to load settings' });
    } finally {
      setLoading(false);
    }
  };

  // Save settings to database
  const saveSettings = async () => {
    try {
      setSaving(true);
      setMessage(null);

      // Map form settings to database format - only include settings with values
      const settingsToUpdate = [
        // General Settings
        { key: 'site_name', value: settings.siteName || '', category: 'GENERAL', description: 'Website name', isactive: true },
        { key: 'site_description', value: settings.siteDescription || '', category: 'GENERAL', description: 'Website description', isactive: true },
        { key: 'site_url', value: settings.siteUrl || '', category: 'GENERAL', description: 'Website URL', isactive: true },
        { key: 'contact_email', value: settings.contactEmail || '', category: 'GENERAL', description: 'Main contact email', isactive: true },

        // Profile Settings
        { key: 'admin_first_name', value: settings.firstName || '', category: 'PROFILE', description: 'Admin first name', isactive: true },
        { key: 'admin_last_name', value: settings.lastName || '', category: 'PROFILE', description: 'Admin last name', isactive: true },
        { key: 'admin_email', value: settings.email || '', category: 'PROFILE', description: 'Admin email address', isactive: true },
        { key: 'admin_phone', value: settings.phone || '', category: 'PROFILE', description: 'Admin phone number', isactive: true },
        { key: 'admin_timezone', value: settings.timezone || 'America/New_York', category: 'PROFILE', description: 'Admin timezone', isactive: true },

        // Notification Settings
        { key: 'email_notifications', value: settings.emailNotifications.toString(), category: 'NOTIFICATIONS', description: 'Enable email notifications', isactive: true },
        { key: 'push_notifications', value: settings.pushNotifications.toString(), category: 'NOTIFICATIONS', description: 'Enable push notifications', isactive: true },
        { key: 'marketing_emails', value: settings.marketingEmails.toString(), category: 'NOTIFICATIONS', description: 'Enable marketing emails', isactive: true },
        { key: 'weekly_reports', value: settings.weeklyReports.toString(), category: 'NOTIFICATIONS', description: 'Enable weekly reports', isactive: true },

        // Security Settings
        { key: 'two_factor_auth', value: settings.twoFactorAuth.toString(), category: 'SECURITY', description: 'Enable two-factor authentication', isactive: true },
        { key: 'session_timeout', value: settings.sessionTimeout.toString(), category: 'SECURITY', description: 'Session timeout in minutes', isactive: true },

        // Appearance Settings
        { key: 'theme', value: settings.theme || 'light', category: 'APPEARANCE', description: 'Application theme', isactive: true },
        { key: 'language', value: settings.language || 'en', category: 'APPEARANCE', description: 'Application language', isactive: true },

        // Integration Settings (only save if they have values)
        ...(settings.googleAnalytics ? [{ key: 'google_analytics', value: settings.googleAnalytics, category: 'INTEGRATIONS', description: 'Google Analytics tracking ID', isactive: true }] : []),
        ...(settings.mailchimpApi ? [{ key: 'mailchimp_api', value: settings.mailchimpApi, category: 'INTEGRATIONS', description: 'Mailchimp API key', isactive: true }] : []),
        ...(settings.slackWebhook ? [{ key: 'slack_webhook', value: settings.slackWebhook, category: 'INTEGRATIONS', description: 'Slack webhook URL', isactive: true }] : []),
      ];

      // Get all existing settings first
      const allSettingsResponse = await fetch('/api/admin/settings?limit=100');
      const allSettingsResult = await allSettingsResponse.json();

      if (!allSettingsResponse.ok) {
        throw new Error('Failed to fetch existing settings');
      }

      const existingSettings = allSettingsResult.success ? allSettingsResult.data : [];

      // Update each setting
      console.log('Saving settings:', settingsToUpdate.length, 'settings to process');

      for (const setting of settingsToUpdate) {
        // Find existing setting by key
        const existingSetting = existingSettings.find((s: any) => s.key === setting.key);

        if (existingSetting) {
          // Update existing setting
          console.log('Updating existing setting:', setting.key, 'with value:', setting.value);
          const updateResponse = await fetch(`/api/admin/settings/${existingSetting.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              value: setting.value,
              description: setting.description,
            }),
          });

          if (!updateResponse.ok) {
            throw new Error(`Failed to update ${setting.key}`);
          }
        } else {
          // Create new setting
          console.log('Creating new setting:', setting.key, 'with value:', setting.value);
          const createResponse = await fetch('/api/admin/settings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(setting),
          });

          if (!createResponse.ok) {
            throw new Error(`Failed to create ${setting.key}`);
          }
        }
      }

      const categoriesSaved = [...new Set(settingsToUpdate.map(s => s.category))];
      setMessage({
        type: 'success',
        text: `Settings saved successfully! Updated categories: ${categoriesSaved.join(', ')}`
      });
    } catch (error) {
      console.error('Error saving settings:', error);
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setSaving(false);
    }
  };

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Load settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Site Name</label>
        <input
          type="text"
          value={settings.siteName}
          onChange={(e) => handleSettingChange('siteName', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700">Site Description</label>
        <textarea
          value={settings.siteDescription}
          onChange={(e) => handleSettingChange('siteDescription', e.target.value)}
          rows={3}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700">Site URL</label>
        <input
          type="url"
          value={settings.siteUrl}
          onChange={(e) => handleSettingChange('siteUrl', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700">Contact Email</label>
        <input
          type="email"
          value={settings.contactEmail}
          onChange={(e) => handleSettingChange('contactEmail', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
    </div>
  );

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
          <label className="block text-sm font-medium text-gray-700">First Name</label>
          <input
            type="text"
            value={settings.firstName}
            onChange={(e) => handleSettingChange('firstName', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700">Last Name</label>
          <input
            type="text"
            value={settings.lastName}
            onChange={(e) => handleSettingChange('lastName', e.target.value)}
            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700">Email</label>
        <input
          type="email"
          value={settings.email}
          onChange={(e) => handleSettingChange('email', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700">Phone</label>
        <input
          type="tel"
          value={settings.phone}
          onChange={(e) => handleSettingChange('phone', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700">Timezone</label>
        <select
          value={settings.timezone}
          onChange={(e) => handleSettingChange('timezone', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
          <option value="America/New_York">Eastern Time</option>
          <option value="America/Chicago">Central Time</option>
          <option value="America/Denver">Mountain Time</option>
          <option value="America/Los_Angeles">Pacific Time</option>
        </select>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Email Notifications</h4>
            <p className="text-sm text-gray-500">Receive notifications via email</p>
          </div>
          <button
            type="button"
            onClick={() => handleSettingChange('emailNotifications', !settings.emailNotifications)}
            className={`${
              settings.emailNotifications ? 'bg-blue-600' : 'bg-gray-200'
            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          >
            <span
              className={`${
                settings.emailNotifications ? 'translate-x-5' : 'translate-x-0'
              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
            />
          </button>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Push Notifications</h4>
            <p className="text-sm text-gray-500">Receive push notifications in browser</p>
          </div>
          <button
            type="button"
            onClick={() => handleSettingChange('pushNotifications', !settings.pushNotifications)}
            className={`${
              settings.pushNotifications ? 'bg-blue-600' : 'bg-gray-200'
            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          >
            <span
              className={`${
                settings.pushNotifications ? 'translate-x-5' : 'translate-x-0'
              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
            />
          </button>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Marketing Emails</h4>
            <p className="text-sm text-gray-500">Receive marketing and promotional emails</p>
          </div>
          <button
            type="button"
            onClick={() => handleSettingChange('marketingEmails', !settings.marketingEmails)}
            className={`${
              settings.marketingEmails ? 'bg-blue-600' : 'bg-gray-200'
            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          >
            <span
              className={`${
                settings.marketingEmails ? 'translate-x-5' : 'translate-x-0'
              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
            />
          </button>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-gray-900">Weekly Reports</h4>
            <p className="text-sm text-gray-500">Receive weekly analytics reports</p>
          </div>
          <button
            type="button"
            onClick={() => handleSettingChange('weeklyReports', !settings.weeklyReports)}
            className={`${
              settings.weeklyReports ? 'bg-blue-600' : 'bg-gray-200'
            } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
          >
            <span
              className={`${
                settings.weeklyReports ? 'translate-x-5' : 'translate-x-0'
              } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
            />
          </button>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
          <p className="text-sm text-gray-500">Add an extra layer of security to your account</p>
        </div>
        <button
          type="button"
          onClick={() => handleSettingChange('twoFactorAuth', !settings.twoFactorAuth)}
          className={`${
            settings.twoFactorAuth ? 'bg-blue-600' : 'bg-gray-200'
          } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
        >
          <span
            className={`${
              settings.twoFactorAuth ? 'translate-x-5' : 'translate-x-0'
            } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
          />
        </button>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700">Session Timeout (minutes)</label>
        <select
          value={settings.sessionTimeout}
          onChange={(e) => handleSettingChange('sessionTimeout', parseInt(e.target.value))}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
          <option value={15}>15 minutes</option>
          <option value={30}>30 minutes</option>
          <option value={60}>1 hour</option>
          <option value={120}>2 hours</option>
          <option value={480}>8 hours</option>
        </select>
      </div>
      
      <div className="space-y-4">
        <button className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Change Password
        </button>
        
        <button className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          Download Account Data
        </button>
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Theme</label>
        <select
          value={settings.theme}
          onChange={(e) => handleSettingChange('theme', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
          <option value="light">Light</option>
          <option value="dark">Dark</option>
          <option value="auto">Auto (System)</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Language</label>
        <select
          value={settings.language}
          onChange={(e) => handleSettingChange('language', e.target.value)}
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        >
          <option value="en">English</option>
          <option value="es">Spanish</option>
          <option value="fr">French</option>
          <option value="de">German</option>
          <option value="ar">Arabic</option>
        </select>
      </div>
    </div>
  );

  const renderIntegrationSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700">Google Analytics Tracking ID</label>
        <input
          type="text"
          value={settings.googleAnalytics}
          onChange={(e) => handleSettingChange('googleAnalytics', e.target.value)}
          placeholder="GA-XXXXXXXXX-X"
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
        <p className="mt-1 text-sm text-gray-500">Enter your Google Analytics tracking ID to enable analytics</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Mailchimp API Key</label>
        <input
          type="password"
          value={settings.mailchimpApi}
          onChange={(e) => handleSettingChange('mailchimpApi', e.target.value)}
          placeholder="Enter your Mailchimp API key"
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
        <p className="mt-1 text-sm text-gray-500">Used for email marketing and newsletter subscriptions</p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Slack Webhook URL</label>
        <input
          type="url"
          value={settings.slackWebhook}
          onChange={(e) => handleSettingChange('slackWebhook', e.target.value)}
          placeholder="https://hooks.slack.com/services/..."
          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        />
        <p className="mt-1 text-sm text-gray-500">Receive notifications in your Slack workspace</p>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'profile':
        return renderProfileSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'security':
        return renderSecuritySettings();
      case 'appearance':
        return renderAppearanceSettings();
      case 'integrations':
        return renderIntegrationSettings();
      default:
        return <div>Settings section not implemented yet.</div>;
    }
  };

  return (
    <div className="py-6">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        {/* Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Settings
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Manage your account settings and preferences
          </p>
        </div>

        <div className="lg:grid lg:grid-cols-12 lg:gap-x-5">
          {/* Settings Navigation */}
          <aside className="py-6 px-2 sm:px-6 lg:py-0 lg:px-0 lg:col-span-3">
            <nav className="space-y-1">
              {settingsSections.map((section) => {
                const Icon = section.icon;
                return (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id)}
                    className={`${
                      activeSection === section.id
                        ? 'bg-blue-50 border-blue-500 text-blue-700'
                        : 'border-transparent text-gray-900 hover:bg-gray-50 hover:text-gray-900'
                    } group border-l-4 px-3 py-2 flex items-center text-sm font-medium w-full text-left`}
                  >
                    <Icon
                      className={`${
                        activeSection === section.id ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                      } flex-shrink-0 -ml-1 mr-3 h-6 w-6`}
                    />
                    <span className="truncate">{section.name}</span>
                  </button>
                );
              })}
            </nav>
          </aside>

          {/* Settings Content */}
          <div className="space-y-6 sm:px-6 lg:px-0 lg:col-span-9">
            <motion.div
              key={activeSection}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3 }}
              className="bg-white shadow rounded-lg"
            >
              <div className="px-4 py-5 sm:p-6">
                <div className="mb-6">
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    {settingsSections.find(s => s.id === activeSection)?.name}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {settingsSections.find(s => s.id === activeSection)?.description}
                  </p>
                </div>
                
                {loading ? (
                  <div className="flex justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : (
                  renderContent()
                )}

                {/* Message Display */}
                {message && (
                  <div className={`mt-4 p-4 rounded-md ${
                    message.type === 'success'
                      ? 'bg-green-50 border border-green-200 text-green-800'
                      : 'bg-red-50 border border-red-200 text-red-800'
                  }`}>
                    {message.text}
                  </div>
                )}

                <div className="mt-6 flex justify-end">
                  <button
                    onClick={saveSettings}
                    disabled={saving || loading}
                    className="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {saving ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
