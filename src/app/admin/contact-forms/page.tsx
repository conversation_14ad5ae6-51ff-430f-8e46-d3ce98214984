'use client';

import { ContactFormsManager } from '@/components/admin/contact-forms/contact-forms-manager';
import { CrudConfig } from '@/components/admin/crud/types';

interface ContactForm {
  id: number
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  isread?: boolean
  readat?: string
  status: string
  createdAt: string
  updatedAt: string
}

const contactFormConfig: CrudConfig<ContactForm> = {
  title: 'Contact Forms',
  description: 'Manage and respond to customer inquiries and contact form submissions',
  endpoint: 'contact-forms', // API endpoint

  columns: [
    {
      key: 'name',
      label: 'Contact Info',
      sortable: true,
      searchable: true,
      renderType: 'contact',
      width: '250px',
      hideable: false, // Always visible
      defaultVisible: true
    },
    {
      key: 'subject',
      label: 'Subject',
      sortable: true,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'message',
      label: 'Message',
      sortable: false,
      searchable: true,
      renderType: 'text',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        statusColors: {
          'New': 'bg-red-100 text-red-800',
          'In Progress': 'bg-blue-100 text-blue-800',
          'Resolved': 'bg-green-100 text-green-800',
          'Closed': 'bg-gray-100 text-gray-800'
        }
      },
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'isread',
      label: 'Read Status',
      sortable: true,
      renderType: 'status',
      renderProps: {
        trueLabel: 'Read',
        falseLabel: 'Unread',
        statusColors: {
          true: 'bg-green-100 text-green-800',
          false: 'bg-red-100 text-red-800'
        }
      },
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'createdat',
      label: 'Submitted',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: true
    },
    {
      key: 'updatedat',
      label: 'Last Updated',
      sortable: true,
      renderType: 'date',
      hideable: true,
      defaultVisible: false
    }
  ],

  // Action buttons for each row
  actions: [
    {
      action: 'reply',
      label: 'Reply',
      icon: 'EnvelopeIcon',
      variant: 'primary',
      tooltip: 'View and reply to contact form'
    },
    {
      action: 'mark-read',
      label: 'Mark as Read',
      icon: 'CheckIcon',
      variant: 'success',
      tooltip: 'Mark as read/unread'
    },
    {
      action: 'delete',
      label: 'Delete',
      icon: 'TrashIcon',
      variant: 'danger',
      tooltip: 'Delete contact form',
      confirmationMessage: 'Are you sure you want to delete this contact form? This action cannot be undone.'
    }
  ],

  fields: [
    {
      key: 'name',
      label: 'Full Name',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'Enter full name'
    },
    {
      key: 'email',
      label: 'Email Address',
      type: 'email',
      required: true,
      searchable: true,
      placeholder: '<EMAIL>'
    },
    {
      key: 'phone',
      label: 'Phone Number',
      type: 'text',
      searchable: false,
      placeholder: '+****************'
    },
    {
      key: 'subject',
      label: 'Subject',
      type: 'text',
      required: true,
      searchable: true,
      placeholder: 'Enter subject'
    },
    {
      key: 'message',
      label: 'Message',
      type: 'textarea',
      required: true,
      searchable: true,
      placeholder: 'Enter your message...',
      rows: 4
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'New', label: 'New' },
        { value: 'In Progress', label: 'In Progress' },
        { value: 'Resolved', label: 'Resolved' },
        { value: 'Closed', label: 'Closed' },
      ],
      defaultValue: 'New',
      searchable: false,
    },
    {
      key: 'isread',
      label: 'Mark as Read',
      type: 'boolean',
      defaultValue: false,
      searchable: false,
    },
  ],

  filters: [
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: '', label: 'All Status' },
        { value: 'New', label: 'New' },
        { value: 'In Progress', label: 'In Progress' },
        { value: 'Resolved', label: 'Resolved' },
        { value: 'Closed', label: 'Closed' },
      ],
    },
    {
      key: 'isread',
      label: 'Read Status',
      type: 'select',
      options: [
        { value: '', label: 'All' },
        { value: 'true', label: 'Read' },
        { value: 'false', label: 'Unread' },
      ],
    },
  ],

  bulkActions: [
    {
      label: 'Mark as Read',
      action: 'mark-read',
      variant: 'success'
    },
    {
      label: 'Mark as Unread',
      action: 'mark-unread',
      variant: 'warning'
    },
    {
      label: 'Mark as Resolved',
      action: 'mark-resolved',
      variant: 'success'
    }
  ],

  permissions: {
    create: true,
    read: true,
    update: true,
    delete: true,
    export: true
  },

  searchPlaceholder: 'Search contact forms by name, email, subject, message...',
  defaultSort: { field: 'createdat', direction: 'desc' }, // Sort by submission date (most recent)
  pageSize: 100, // Show more records to see all original messages
  enableSearch: true,
  enableFilters: true,
  enableBulkActions: true,
  enableExport: true,
  enableViewControls: true,
  enableDensityControls: true,
  enableColumnVisibility: true,
  defaultViewSettings: {
    mode: 'list',
    density: 'comfortable',
    visibleColumns: ['name', 'subject', 'message', 'status', 'isread', 'createdat']
  },

  // Form layout configuration
  formLayout: {
    type: 'compact',
    columns: 2,
    sections: [
      {
        title: 'Contact Information',
        fields: ['name', 'email', 'phone']
      },
      {
        title: 'Message Details',
        fields: ['subject', 'message']
      },
      {
        title: 'Status & Management',
        fields: ['status', 'isread']
      }
    ]
  }
};

export default function ContactFormsPage() {
  return <ContactFormsManager config={contactFormConfig} />;
}


