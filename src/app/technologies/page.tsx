'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  StarIcon,
  ArrowTopRightOnSquareIcon,
  CodeBracketIcon,
  DevicePhoneMobileIcon,
  CloudIcon,
  CircleStackIcon,
} from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { useState } from 'react';
import { useStaticContent } from '@/lib/hooks/use-static-content';

const technologies = [
  {
    id: 'react',
    name: 'React',
    description: 'A JavaScript library for building user interfaces with component-based architecture and virtual DOM.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 5,
    projectsUsed: 45,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg',
    website: 'https://reactjs.org',
    documentation: 'https://reactjs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'UI', 'SPA', 'Component-based'],
    useCases: ['Single Page Applications', 'Interactive UIs', 'Component Libraries', 'Progressive Web Apps'],
    advantages: [
      'Virtual DOM for optimal performance',
      'Large ecosystem and community',
      'Reusable component architecture',
      'Strong developer tools'
    ]
  },
  {
    id: 'nextjs',
    name: 'Next.js',
    description: 'The React framework for production with server-side rendering, static site generation, and full-stack capabilities.',
    category: 'Frontend',
    type: 'Framework',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 4,
    projectsUsed: 32,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg',
    website: 'https://nextjs.org',
    documentation: 'https://nextjs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['React', 'SSR', 'SSG', 'Full-stack'],
    useCases: ['E-commerce Sites', 'Corporate Websites', 'Blogs', 'Web Applications'],
    advantages: [
      'Built-in SEO optimization',
      'Automatic code splitting',
      'API routes for backend logic',
      'Excellent performance out of the box'
    ]
  },
  {
    id: 'nodejs',
    name: 'Node.js',
    description: 'JavaScript runtime built on Chrome\'s V8 JavaScript engine for scalable server-side development.',
    category: 'Backend',
    type: 'Runtime',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 6,
    projectsUsed: 38,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg',
    website: 'https://nodejs.org',
    documentation: 'https://nodejs.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'Server', 'API', 'Microservices'],
    useCases: ['REST APIs', 'Real-time Applications', 'Microservices', 'Command Line Tools'],
    advantages: [
      'Non-blocking I/O operations',
      'Large package ecosystem (npm)',
      'JavaScript everywhere',
      'High performance for I/O intensive apps'
    ]
  },
  {
    id: 'typescript',
    name: 'TypeScript',
    description: 'Typed superset of JavaScript that compiles to plain JavaScript for better development experience.',
    category: 'Language',
    type: 'Programming Language',
    proficiencyLevel: 'Expert',
    yearsOfExperience: 4,
    projectsUsed: 42,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg',
    website: 'https://www.typescriptlang.org',
    documentation: 'https://www.typescriptlang.org/docs',
    isActive: true,
    isFeatured: true,
    tags: ['JavaScript', 'Type Safety', 'Development'],
    useCases: ['Large Applications', 'Team Development', 'Enterprise Software', 'Library Development'],
    advantages: [
      'Static type checking',
      'Better IDE support and autocomplete',
      'Easier refactoring and maintenance',
      'Catches errors at compile time'
    ]
  },
  {
    id: 'python',
    name: 'Python',
    description: 'High-level programming language known for its simplicity and versatility in various domains.',
    category: 'Language',
    type: 'Programming Language',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 7,
    projectsUsed: 28,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/python/python-original.svg',
    website: 'https://www.python.org',
    documentation: 'https://docs.python.org',
    isActive: true,
    isFeatured: false,
    tags: ['AI/ML', 'Data Science', 'Backend', 'Automation'],
    useCases: ['Machine Learning', 'Data Analysis', 'Web Development', 'Automation Scripts'],
    advantages: [
      'Simple and readable syntax',
      'Extensive library ecosystem',
      'Great for rapid prototyping',
      'Strong community support'
    ]
  },
  {
    id: 'postgresql',
    name: 'PostgreSQL',
    description: 'Advanced open-source relational database with strong standards compliance and extensibility.',
    category: 'Database',
    type: 'Database',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 5,
    projectsUsed: 35,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg',
    website: 'https://www.postgresql.org',
    documentation: 'https://www.postgresql.org/docs',
    isActive: true,
    isFeatured: false,
    tags: ['SQL', 'ACID', 'Scalable', 'Open Source'],
    useCases: ['Web Applications', 'Data Warehousing', 'Analytics', 'Enterprise Systems'],
    advantages: [
      'ACID compliance and reliability',
      'Advanced SQL features',
      'Extensible with custom functions',
      'Excellent performance and scalability'
    ]
  },
  {
    id: 'aws',
    name: 'AWS',
    description: 'Amazon Web Services cloud computing platform offering a wide range of infrastructure services.',
    category: 'Cloud',
    type: 'Platform',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 4,
    projectsUsed: 25,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg',
    website: 'https://aws.amazon.com',
    documentation: 'https://docs.aws.amazon.com',
    isActive: true,
    isFeatured: true,
    tags: ['Cloud', 'Infrastructure', 'Scalable', 'DevOps'],
    useCases: ['Web Hosting', 'Data Storage', 'Machine Learning', 'Serverless Computing'],
    advantages: [
      'Global infrastructure and availability',
      'Comprehensive service portfolio',
      'Pay-as-you-use pricing model',
      'Enterprise-grade security'
    ]
  },
  {
    id: 'docker',
    name: 'Docker',
    description: 'Platform for developing, shipping, and running applications using containerization technology.',
    category: 'DevOps',
    type: 'Tool',
    proficiencyLevel: 'Advanced',
    yearsOfExperience: 3,
    projectsUsed: 30,
    logo: 'https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg',
    website: 'https://www.docker.com',
    documentation: 'https://docs.docker.com',
    isActive: true,
    isFeatured: false,
    tags: ['Containerization', 'DevOps', 'Deployment'],
    useCases: ['Application Deployment', 'Development Environment', 'Microservices', 'CI/CD'],
    advantages: [
      'Consistent environments across platforms',
      'Lightweight and efficient',
      'Easy scaling and orchestration',
      'Simplified deployment process'
    ]
  }
];

const categories = ['All', 'Frontend', 'Backend', 'Language', 'Database', 'Cloud', 'DevOps'];
const proficiencyLevels = ['All', 'Beginner', 'Intermediate', 'Advanced', 'Expert'];

const getProficiencyColor = (level: string) => {
  switch (level) {
    case 'Expert': return 'bg-green-100 text-green-800';
    case 'Advanced': return 'bg-blue-100 text-blue-800';
    case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
    case 'Beginner': return 'bg-gray-100 text-gray-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'Frontend': return CodeBracketIcon;
    case 'Backend': return CloudIcon;
    case 'Database': return CircleStackIcon;
    case 'DevOps': return CloudIcon;
    default: return CodeBracketIcon;
  }
};

export default function TechnologiesPage() {
  const { getContent } = useStaticContent();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedProficiency, setSelectedProficiency] = useState('All');

  const filteredTechnologies = technologies.filter(tech => {
    const matchesSearch = tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tech.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tech.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || tech.category === selectedCategory;
    const matchesProficiency = selectedProficiency === 'All' || tech.proficiencyLevel === selectedProficiency;
    
    return matchesSearch && matchesCategory && matchesProficiency;
  });

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="pt-20">
        {/* Hero Section */}
        <section className="relative py-24 bg-gradient-to-br from-gray-50 via-white to-gray-100">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center max-w-4xl mx-auto"
            >
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl lg:text-7xl"
              >
                {getContent('technologies', 'hero', 'title', 'Our')} <span className="gradient-text">{getContent('technologies', 'hero', 'title_highlight', 'Technologies')}</span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="mt-6 text-lg leading-8 text-gray-600 sm:text-xl lg:text-2xl"
              >
                {getContent('technologies', 'hero', 'subtitle', 'We leverage cutting-edge technologies and frameworks to build robust, scalable, and future-proof solutions for our clients.')}
              </motion.p>
            </motion.div>
          </div>
        </section>

        {/* Stats Section */}
        <section className="py-16 bg-white">
          <div className="container">
            <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
              {[
                { label: 'Technologies Mastered', value: '100+' },
                { label: 'Years Combined Experience', value: '50+' },
                { label: 'Projects Delivered', value: '500+' },
                { label: 'Expert Level Skills', value: '25+' }
              ].map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-3xl font-bold text-blue-600 sm:text-4xl">
                    {stat.value}
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Filters Section */}
        <section className="py-8 bg-gray-50">
          <div className="container">
            <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search technologies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              {/* Filters */}
              <div className="flex gap-4">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>

                <select
                  value={selectedProficiency}
                  onChange={(e) => setSelectedProficiency(e.target.value)}
                  className="block pl-3 pr-10 py-2 text-base border border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
                >
                  {proficiencyLevels.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        </section>

        {/* Technologies Grid */}
        <section className="py-16 bg-white">
          <div className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredTechnologies.map((tech, index) => {
                const CategoryIcon = getCategoryIcon(tech.category);
                return (
                  <motion.div
                    key={tech.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group bg-white p-6 rounded-2xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-300"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <img
                            src={tech.logo}
                            alt={tech.name}
                            className="w-10 h-10 object-contain"
                          />
                        </div>
                        <div>
                          <h3 className="text-lg font-medium text-gray-900">{tech.name}</h3>
                          <p className="text-sm text-gray-500">{tech.category}</p>
                        </div>
                      </div>
                      {tech.isFeatured && (
                        <StarIcon className="w-5 h-5 text-yellow-400 fill-current" />
                      )}
                    </div>

                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                      {tech.description}
                    </p>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Proficiency</span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(tech.proficiencyLevel)}`}>
                          {tech.proficiencyLevel}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Experience</span>
                        <span className="text-sm font-medium text-gray-900">{tech.yearsOfExperience} years</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Projects</span>
                        <span className="text-sm font-medium text-gray-900">{tech.projectsUsed}</span>
                      </div>
                    </div>

                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {tech.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            {tag}
                          </span>
                        ))}
                        {tech.tags.length > 3 && (
                          <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            +{tech.tags.length - 3}
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <Link
                        href={`/technologies/${tech.id}`}
                        className="text-blue-600 hover:text-blue-700 font-medium text-sm group/link"
                      >
                        Learn More
                        <ArrowTopRightOnSquareIcon className="ml-1 h-4 w-4 inline transition-transform group-hover/link:translate-x-0.5 group-hover/link:-translate-y-0.5" />
                      </Link>
                      <a
                        href={tech.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                        title="Official Website"
                      >
                        <ArrowTopRightOnSquareIcon className="h-4 w-4" />
                      </a>
                    </div>
                  </motion.div>
                );
              })}
            </div>

            {/* Empty State */}
            {filteredTechnologies.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-400 mb-4">
                  <CodeBracketIcon className="mx-auto h-12 w-12" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No technologies found</h3>
                <p className="text-gray-600">
                  Try adjusting your search criteria or filters.
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Featured Technologies */}
        <section className="py-16 bg-gray-50">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {getContent('technologies', 'categories', 'title', 'Technology')} <span className="gradient-text">{getContent('technologies', 'categories', 'title_highlight', 'Stack')}</span>
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                {getContent('technologies', 'categories', 'subtitle', 'Comprehensive expertise across the full technology spectrum')}
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {technologies.filter(tech => tech.isFeatured).map((tech, index) => (
                <motion.div
                  key={tech.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center group"
                >
                  <div className="flex items-center justify-center w-20 h-20 bg-white rounded-2xl mx-auto mb-4 shadow-lg group-hover:shadow-xl transition-shadow">
                    <img
                      src={tech.logo}
                      alt={tech.name}
                      className="w-12 h-12 object-contain"
                    />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {tech.name}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2">
                    {tech.yearsOfExperience} years experience
                  </p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getProficiencyColor(tech.proficiencyLevel)}`}>
                    {tech.proficiencyLevel}
                  </span>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="container">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="text-center"
            >
              <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
                Need Help Choosing the Right Technology?
              </h2>
              <p className="mt-4 text-lg text-blue-100 max-w-3xl mx-auto">
                Our experts can help you select the perfect technology stack for your project.
                Let's discuss your requirements and find the best solution.
              </p>
              <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  href="/contact"
                  className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-gray-50 transition-colors"
                >
                  Get Technology Consultation
                </Link>
                <Link
                  href="/services"
                  className="inline-flex items-center px-8 py-3 border-2 border-white text-base font-medium rounded-md text-white hover:bg-white hover:text-blue-600 transition-colors"
                >
                  View Our Services
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
