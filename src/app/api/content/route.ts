import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest
} from '@/lib/api-utils'

// Content structure interfaces
export interface ContentSlide {
  id?: string
  title: string
  subtitle: string
  buttonText: string
  buttonUrl: string
  imageUrl: string
  displayOrder: number
  isActive: boolean
}

export interface ContentSection {
  id: string
  type: 'hero' | 'about' | 'services' | 'testimonials' | 'legal' | 'projects' | 'technologies' | 'team' | 'blog' | 'contact'
  title: string
  subtitle?: string
  content?: string
  fields: Record<string, any>
  slides?: ContentSlide[]
  isActive: boolean
  displayOrder: number
}

export interface ContentPage {
  id: string
  name: string
  slug: string
  title: string
  metaDescription?: string
  sections: ContentSection[]
}

export interface ContentData {
  pages: ContentPage[]
  lastUpdated: string
}

// GET /api/content - Get all content organized by pages
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  try {
    // Fetch all content types
    const [
      services,
      testimonials,
      legalPages,
      projects,
      technologies,
      teamMembers,
      blogPosts,
      contactForms
    ] = await Promise.all([
      prisma.services.findMany({
        include: {
          categories: true
        },
        orderBy: { displayorder: 'asc' }
      }),
      prisma.testimonials.findMany({
        include: {
          clients: true
        },
        orderBy: { displayorder: 'asc' }
      }),
      prisma.legalpages.findMany({
        include: {
          legalpagesections: {
            orderBy: { displayorder: 'asc' }
          }
        },
        orderBy: { createdat: 'desc' }
      }),
      prisma.projects.findMany({
        include: {
          clients: true,
          projecttechnologies: {
            include: {
              technologies: true
            }
          }
        },
        orderBy: { displayorder: 'asc' }
      }),
      prisma.technologies.findMany({
        orderBy: { displayorder: 'asc' }
      }),
      prisma.teammembers.findMany({
        orderBy: { displayorder: 'asc' }
      }),
      prisma.blogposts.findMany({
        where: { ispublished: true },
        orderBy: { publishedat: 'desc' }
      }),
      prisma.contactforms.findMany({
        orderBy: { createdat: 'desc' },
        take: 10 // Latest contact forms for reference
      })
    ])

    // Transform data into content structure
    const pages: ContentPage[] = [
      // Home Page
      {
        id: 'home',
        name: 'Home',
        slug: 'home',
        title: 'Home Page',
        metaDescription: 'Main landing page content',
        sections: [
          // Services section for home page
          {
            id: 'home-services',
            type: 'services' as const,
            title: 'Our Services',
            subtitle: 'What we offer',
            content: 'Comprehensive software development services',
            fields: {
              sectionTitle: 'Our Services',
              sectionSubtitle: 'What we offer',
              sectionDescription: 'Comprehensive software development services to help your business thrive'
            },
            slides: [],
            isActive: true,
            displayOrder: 2
          },
          // Projects section for home page
          {
            id: 'home-projects',
            type: 'projects' as const,
            title: 'Featured Projects',
            subtitle: 'Our latest work',
            content: 'Showcase of our recent projects',
            fields: {
              sectionTitle: 'Featured Projects',
              sectionSubtitle: 'Our latest work',
              sectionDescription: 'Discover our recent projects and success stories'
            },
            slides: [],
            isActive: true,
            displayOrder: 3
          },
          // Testimonials section for home page
          {
            id: 'home-testimonials',
            type: 'testimonials' as const,
            title: 'Client Testimonials',
            subtitle: 'What our clients say',
            content: 'Reviews and feedback from our satisfied clients',
            fields: {
              sectionTitle: 'Client Testimonials',
              sectionSubtitle: 'What our clients say',
              sectionDescription: 'Read what our clients have to say about working with us'
            },
            slides: [],
            isActive: true,
            displayOrder: 4
          },
          // Technologies section for home page
          {
            id: 'home-technologies',
            type: 'technologies' as const,
            title: 'Technologies We Use',
            subtitle: 'Our tech stack',
            content: 'Modern technologies and frameworks we work with',
            fields: {
              sectionTitle: 'Technologies We Use',
              sectionSubtitle: 'Our tech stack',
              sectionDescription: 'We use the latest technologies to build exceptional solutions'
            },
            slides: [],
            isActive: true,
            displayOrder: 5
          },
          // Team section for home page
          {
            id: 'home-team',
            type: 'team' as const,
            title: 'Meet Our Team',
            subtitle: 'The people behind the magic',
            content: 'Our talented team of developers and designers',
            fields: {
              sectionTitle: 'Meet Our Team',
              sectionSubtitle: 'The people behind the magic',
              sectionDescription: 'Get to know the talented individuals who make it all happen'
            },
            slides: [],
            isActive: true,
            displayOrder: 6
          },
          // Blog section for home page
          {
            id: 'home-blog',
            type: 'blog' as const,
            title: 'Latest Blog Posts',
            subtitle: 'Insights and updates',
            content: 'Our latest thoughts and industry insights',
            fields: {
              sectionTitle: 'Latest Blog Posts',
              sectionSubtitle: 'Insights and updates',
              sectionDescription: 'Stay updated with our latest insights and industry news'
            },
            slides: [],
            isActive: true,
            displayOrder: 7
          },
          // Contact section for home page
          {
            id: 'home-contact',
            type: 'contact' as const,
            title: 'Get In Touch',
            subtitle: 'Ready to start your project?',
            content: 'Contact us to discuss your project requirements',
            fields: {
              sectionTitle: 'Get In Touch',
              sectionSubtitle: 'Ready to start your project?',
              sectionDescription: 'Contact us today to discuss your project requirements',
              email: '<EMAIL>',
              phone: '+****************',
              address: '123 Tech Street, San Francisco, CA 94105'
            },
            slides: [],
            isActive: true,
            displayOrder: 8
          }
        ]
      },

      // Services Page
      {
        id: 'services',
        name: 'Services',
        slug: 'services',
        title: 'Services Page',
        metaDescription: 'Our services content',
        sections: services.map((service, index) => ({
          id: `service-${service.id}`,
          type: 'services' as const,
          title: service.name,
          subtitle: '',
          content: service.description,
          fields: {
            name: service.name,
            description: service.description,
            iconClass: service.iconclass,
            price: service.price?.toString(),
            discountRate: service.discountrate,
            totalDiscount: service.totaldiscount,
            manager: service.manager
          },
          slides: [],
          isActive: service.isactive,
          displayOrder: index
        }))
      },
      // Projects Page
      {
        id: 'projects',
        name: 'Projects',
        slug: 'projects',
        title: 'Projects Page',
        metaDescription: 'Our portfolio and project showcase',
        sections: [
          // Projects header section
          {
            id: 'projects-header',
            type: 'projects' as const,
            title: 'Our Projects',
            subtitle: 'Portfolio showcase',
            content: 'Explore our diverse portfolio of successful projects',
            fields: {
              sectionTitle: 'Our Projects',
              sectionSubtitle: 'Portfolio showcase',
              sectionDescription: 'Explore our diverse portfolio of successful projects and see how we help businesses achieve their goals'
            },
            slides: [],
            isActive: true,
            displayOrder: 1
          },
          // Individual projects
          ...projects.map((project, index) => ({
            id: `project-${project.id}`,
            type: 'projects' as const,
            title: project.name,
            subtitle: project.clients?.companyname || '',
            content: project.description,
            fields: {
              name: project.name,
              description: project.description,
              goals: project.projgoals,
              imageUrl: project.imageurl,
              projectUrl: project.projecturl,
              githubUrl: project.githuburl,
              tags: project.tags,
              startDate: project.projstartdate?.toISOString(),
              completionDate: project.projcompletiondate?.toISOString(),
              estimateCost: project.estimatecost,
              estimateTime: project.estimatetime,
              status: project.status,
              isFeatured: project.isfeatured,
              isPublic: project.ispublic,
              clientName: project.clients?.companyname,
              technologies: project.projecttechnologies.map(pt => pt.technologies.name).join(', ')
            },
            slides: [],
            isActive: true,
            displayOrder: index + 2
          }))
        ]
      },
      // Technologies Page
      {
        id: 'technologies',
        name: 'Technologies',
        slug: 'technologies',
        title: 'Technologies Page',
        metaDescription: 'Technologies and tools we use',
        sections: [
          // Technologies header section
          {
            id: 'technologies-header',
            type: 'technologies' as const,
            title: 'Our Technology Stack',
            subtitle: 'Modern tools and frameworks',
            content: 'We use cutting-edge technologies to build exceptional solutions',
            fields: {
              sectionTitle: 'Our Technology Stack',
              sectionSubtitle: 'Modern tools and frameworks',
              sectionDescription: 'We leverage the latest technologies and best practices to deliver high-quality solutions'
            },
            slides: [],
            isActive: true,
            displayOrder: 1
          },
          // Individual technologies
          ...technologies.map((tech, index) => ({
            id: `technology-${tech.id}`,
            type: 'technologies' as const,
            title: tech.name,
            subtitle: tech.category || '',
            content: tech.description || '',
            fields: {
              name: tech.name,
              description: tech.description,
              category: tech.category,
              version: tech.version,
              logoUrl: tech.logourl,
              websiteUrl: tech.websiteurl,
              documentationUrl: tech.documentationurl,
              isActive: tech.isactive,
              proficiencyLevel: tech.proficiencylevel,
              yearsOfExperience: tech.yearsofexperience
            },
            slides: [],
            isActive: tech.isactive,
            displayOrder: index + 2
          }))
        ]
      },
      // Team Page
      {
        id: 'team',
        name: 'Team',
        slug: 'team',
        title: 'Team Page',
        metaDescription: 'Meet our talented team members',
        sections: [
          // Team header section
          {
            id: 'team-header',
            type: 'team' as const,
            title: 'Meet Our Team',
            subtitle: 'The people behind the magic',
            content: 'Get to know the talented individuals who make it all happen',
            fields: {
              sectionTitle: 'Meet Our Team',
              sectionSubtitle: 'The people behind the magic',
              sectionDescription: 'Our diverse team of experts brings together years of experience and passion for technology'
            },
            slides: [],
            isActive: true,
            displayOrder: 1
          },
          // Individual team members
          ...teamMembers.map((member, index) => ({
            id: `team-${member.id}`,
            type: 'team' as const,
            title: `${member.firstname} ${member.lastname}`,
            subtitle: member.position || '',
            content: member.bio || '',
            fields: {
              firstName: member.firstname,
              lastName: member.lastname,
              position: member.position,
              bio: member.bio,
              email: member.email,
              phone: member.phone,
              photoUrl: member.photourl,
              linkedinUrl: member.linkedinurl,
              githubUrl: member.githuburl,
              twitterUrl: member.twitterurl,
              skills: member.skills,
              experience: member.experience,
              education: member.education,
              certifications: member.certifications,
              isActive: member.isactive
            },
            slides: [],
            isActive: member.isactive,
            displayOrder: index + 2
          }))
        ]
      },
      // Blog Page
      {
        id: 'blog',
        name: 'Blog',
        slug: 'blog',
        title: 'Blog Page',
        metaDescription: 'Latest insights and industry news',
        sections: [
          // Blog header section
          {
            id: 'blog-header',
            type: 'blog' as const,
            title: 'Our Blog',
            subtitle: 'Insights and updates',
            content: 'Stay updated with our latest insights, tutorials, and industry news',
            fields: {
              sectionTitle: 'Our Blog',
              sectionSubtitle: 'Insights and updates',
              sectionDescription: 'Discover the latest trends, best practices, and insights from our team of experts'
            },
            slides: [],
            isActive: true,
            displayOrder: 1
          },
          // Individual blog posts
          ...blogPosts.map((post, index) => ({
            id: `blog-${post.id}`,
            type: 'blog' as const,
            title: post.title,
            subtitle: post.excerpt || '',
            content: post.content,
            fields: {
              title: post.title,
              content: post.content,
              slug: post.slug,
              excerpt: post.excerpt,
              featuredImageUrl: post.featuredimageurl,
              authorId: post.authorid,
              isPublished: post.ispublished,
              publishedAt: post.publishedat?.toISOString(),
              categories: post.categories,
              tags: post.tags
            },
            slides: [],
            isActive: post.ispublished || false,
            displayOrder: index + 2
          }))
        ]
      },
      // Contact Page
      {
        id: 'contact',
        name: 'Contact',
        slug: 'contact',
        title: 'Contact Page',
        metaDescription: 'Get in touch with us',
        sections: [
          // Contact header section
          {
            id: 'contact-header',
            type: 'contact' as const,
            title: 'Get In Touch',
            subtitle: 'Ready to start your project?',
            content: 'Contact us today to discuss your project requirements and get a free consultation',
            fields: {
              sectionTitle: 'Get In Touch',
              sectionSubtitle: 'Ready to start your project?',
              sectionDescription: 'We\'d love to hear about your project and discuss how we can help bring your ideas to life',
              email: '<EMAIL>',
              phone: '+****************',
              address: '123 Tech Street, San Francisco, CA 94105',
              businessHours: 'Mon-Fri 9AM-6PM EST',
              responseTime: 'We respond within 24 hours'
            },
            slides: [],
            isActive: true,
            displayOrder: 1
          },
          // Contact form section
          {
            id: 'contact-form',
            type: 'contact' as const,
            title: 'Send us a message',
            subtitle: 'Project inquiry form',
            content: 'Fill out the form below and we\'ll get back to you as soon as possible',
            fields: {
              sectionTitle: 'Send us a message',
              sectionSubtitle: 'Project inquiry form',
              sectionDescription: 'Tell us about your project and we\'ll provide you with a detailed proposal',
              formFields: 'firstName,lastName,email,phone,company,projectType,budget,timeline,message',
              submitButtonText: 'Send Message',
              successMessage: 'Thank you for your message! We\'ll get back to you soon.',
              errorMessage: 'Sorry, there was an error sending your message. Please try again.'
            },
            slides: [],
            isActive: true,
            displayOrder: 2
          }
        ]
      },
      // Legal Pages
      {
        id: 'legal',
        name: 'Legal',
        slug: 'legal',
        title: 'Legal Pages',
        metaDescription: 'Legal documentation',
        sections: legalPages.map((legal, index) => ({
          id: `legal-${legal.id}`,
          type: 'legal' as const,
          title: legal.title,
          subtitle: '',
          content: legal.content,
          fields: {
            title: legal.title,
            slug: legal.slug,
            content: legal.content,
            metaDescription: legal.metadescription
          },
          slides: [],
          isActive: legal.isactive,
          displayOrder: legal.displayorder || index
        }))
      }
    ]

    const contentData: ContentData = {
      pages,
      lastUpdated: new Date().toISOString()
    }

    return successResponse(contentData, 'Content retrieved successfully')
  } catch (error) {
    console.error('Error fetching content:', error)
    throw error
  }
})

// PUT /api/content - Update content
export const PUT = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()
  const { pages } = body as { pages: ContentPage[] }

  try {
    // Process each page and update corresponding database tables
    for (const page of pages) {
      for (const section of page.sections) {
        switch (section.type) {
          case 'services':
            await updateServiceSection(section)
            break
          case 'legal':
            await updateLegalSection(section)
            break
          case 'projects':
            await updateProjectSection(section)
            break
          case 'technologies':
            await updateTechnologySection(section)
            break
          case 'team':
            await updateTeamSection(section)
            break
          case 'blog':
            await updateBlogSection(section)
            break
          case 'contact':
            await updateContactSection(section)
            break
          case 'testimonials':
            await updateTestimonialSection(section)
            break
        }
      }
    }

    return successResponse({ success: true }, 'Content updated successfully')
  } catch (error) {
    console.error('Error updating content:', error)
    throw error
  }
})

// Helper functions for updating different section types


async function updateServiceSection(section: ContentSection) {
  // Skip header sections and home page sections
  if (section.id === 'home-services' || !section.id.startsWith('service-')) return

  const serviceId = parseInt(section.id.replace('service-', ''))

  // Skip if serviceId is not a valid number
  if (isNaN(serviceId)) return

  await prisma.services.update({
    where: { id: BigInt(serviceId) },
    data: {
      name: section.title,
      description: section.content || '',
      iconclass: section.fields.iconClass || null,
      manager: section.fields.manager || null,
      isactive: section.isActive,
      updatedat: new Date()
    }
  })
}

async function updateLegalSection(section: ContentSection) {
  // Skip if not a valid legal section ID
  if (!section.id.startsWith('legal-')) return

  const legalId = parseInt(section.id.replace('legal-', ''))

  // Skip if legalId is not a valid number
  if (isNaN(legalId)) return

  await prisma.legalpages.update({
    where: { id: BigInt(legalId) },
    data: {
      title: section.title,
      content: section.content || '',
      metadescription: section.fields.metaDescription,
      isactive: section.isActive,
      updatedat: new Date()
    }
  })
}

async function updateProjectSection(section: ContentSection) {
  // Skip header sections and only update actual project records
  if (section.id === 'projects-header' || !section.id.startsWith('project-')) return

  const projectId = parseInt(section.id.replace('project-', ''))

  // Skip if projectId is not a valid number
  if (isNaN(projectId)) return

  await prisma.projects.update({
    where: { id: BigInt(projectId) },
    data: {
      name: section.title,
      description: section.content || '',
      projgoals: section.fields.goals || null,
      imageurl: section.fields.imageUrl || null,
      projecturl: section.fields.projectUrl || null,
      githuburl: section.fields.githubUrl || null,
      tags: section.fields.tags || null,
      estimatecost: section.fields.estimateCost ? parseFloat(section.fields.estimateCost) : null,
      estimatetime: section.fields.estimateTime || null,
      status: section.fields.status || null,
      isfeatured: section.fields.isFeatured || false,
      ispublic: section.fields.isPublic || false,
      updatedat: new Date()
    }
  })
}

async function updateTechnologySection(section: ContentSection) {
  // Skip header sections and only update actual technology records
  if (section.id === 'technologies-header' || !section.id.startsWith('technology-')) return

  const techId = parseInt(section.id.replace('technology-', ''))

  // Skip if techId is not a valid number
  if (isNaN(techId)) return

  await prisma.technologies.update({
    where: { id: BigInt(techId) },
    data: {
      name: section.title,
      description: section.content || '',
      iconurl: section.fields.iconUrl || null,
      displayorder: section.displayOrder || 0,
      isactive: section.isActive,
      updatedat: new Date()
    }
  })
}

async function updateTeamSection(section: ContentSection) {
  // Skip header sections and only update actual team member records
  if (section.id === 'team-header' || !section.id.startsWith('team-')) return

  const teamId = parseInt(section.id.replace('team-', ''))

  // Skip if teamId is not a valid number
  if (isNaN(teamId)) return

  await prisma.teammembers.update({
    where: { id: BigInt(teamId) },
    data: {
      name: section.title,
      position: section.fields.position || '',
      bio: section.content || null,
      email: section.fields.email || null,
      phone: section.fields.phone || '',
      photourl: section.fields.photoUrl || null,
      linkedinurl: section.fields.linkedinUrl || null,
      githuburl: section.fields.githubUrl || null,
      twitterurl: section.fields.twitterUrl || null,
      displayorder: section.displayOrder || 0,
      isactive: section.isActive,
      updatedat: new Date()
    }
  })
}

async function updateBlogSection(section: ContentSection) {
  // Skip header sections and only update actual blog post records
  if (section.id === 'blog-header' || !section.id.startsWith('blog-')) return

  const blogId = parseInt(section.id.replace('blog-', ''))

  // Skip if blogId is not a valid number
  if (isNaN(blogId)) return

  await prisma.blogposts.update({
    where: { id: BigInt(blogId) },
    data: {
      title: section.title,
      content: section.content || '',
      excerpt: section.fields.excerpt || null,
      featuredimageurl: section.fields.featuredImageUrl || null,
      categories: section.fields.categories || null,
      tags: section.fields.tags || null,
      ispublished: section.isActive,
      updatedat: new Date()
    }
  })
}

async function updateContactSection(section: ContentSection) {
  // Contact sections are mostly static content, no database updates needed
  // This could be extended to update site settings or contact information
  console.log('Contact section update requested:', section.id)
}

async function updateTestimonialSection(section: ContentSection) {
  // Skip if this is a home page testimonials section (no specific testimonial ID)
  if (section.id === 'home-testimonials' || !section.id.startsWith('testimonial-')) return

  const testimonialId = parseInt(section.id.replace('testimonial-', ''))

  // Skip if testimonialId is not a valid number
  if (isNaN(testimonialId)) return

  await prisma.testimonials.update({
    where: { id: BigInt(testimonialId) },
    data: {
      content: section.content || '',
      rating: section.fields.rating || 5,
      isfeatured: section.fields.isFeatured || false,
      displayorder: section.displayOrder,
      updatedat: new Date()
    }
  })
}
