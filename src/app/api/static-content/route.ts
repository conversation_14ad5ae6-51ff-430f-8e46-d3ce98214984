import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/static-content - Fetch static content
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = searchParams.get('page')
    const section = searchParams.get('section')

    let whereClause: any = {
      isactive: true
    }

    if (page) {
      whereClause.page = page
    }

    if (section) {
      whereClause.section = section
    }

    const staticContent = await prisma.staticcontent.findMany({
      where: whereClause,
      orderBy: [
        { page: 'asc' },
        { section: 'asc' },
        { displayorder: 'asc' }
      ]
    })

    // Group content by page and section for easier consumption
    const groupedContent: Record<string, Record<string, Record<string, any>>> = {}

    staticContent.forEach(item => {
      if (!groupedContent[item.page]) {
        groupedContent[item.page] = {}
      }
      if (!groupedContent[item.page][item.section]) {
        groupedContent[item.page][item.section] = {}
      }
      groupedContent[item.page][item.section][item.contentkey] = {
        id: item.id.toString(),
        content: item.content,
        contenttype: item.contenttype,
        displayorder: item.displayorder
      }
    })

    return NextResponse.json({
      success: true,
      data: groupedContent,
      raw: staticContent.map(item => ({
        ...item,
        id: item.id.toString()
      }))
    })

  } catch (error) {
    console.error('Error fetching static content:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch static content' },
      { status: 500 }
    )
  }
}

// PUT /api/static-content - Update static content
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { updates } = body

    if (!Array.isArray(updates)) {
      return NextResponse.json(
        { success: false, error: 'Updates must be an array' },
        { status: 400 }
      )
    }

    // Process updates in a transaction
    const results = await prisma.$transaction(async (tx) => {
      const updatePromises = updates.map(async (update: any) => {
        const { page, section, contentkey, content, contenttype } = update

        if (!page || !section || !contentkey || content === undefined) {
          throw new Error('Missing required fields: page, section, contentkey, content')
        }

        return await tx.staticcontent.upsert({
          where: {
            page_section_contentkey: {
              page,
              section,
              contentkey
            }
          },
          update: {
            content,
            contenttype: contenttype || 'text',
            updatedat: new Date()
          },
          create: {
            page,
            section,
            contentkey,
            content,
            contenttype: contenttype || 'text',
            displayorder: 0
          }
        })
      })

      return await Promise.all(updatePromises)
    })

    return NextResponse.json({
      success: true,
      message: `Updated ${results.length} static content entries`,
      data: results.map(item => ({
        ...item,
        id: item.id.toString()
      }))
    })

  } catch (error) {
    console.error('Error updating static content:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update static content' },
      { status: 500 }
    )
  }
}

// POST /api/static-content - Create new static content
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { page, section, contentkey, content, contenttype = 'text', displayorder = 0 } = body

    if (!page || !section || !contentkey || content === undefined) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields: page, section, contentkey, content' },
        { status: 400 }
      )
    }

    const staticContent = await prisma.staticcontent.create({
      data: {
        page,
        section,
        contentkey,
        content,
        contenttype,
        displayorder
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...staticContent,
        id: staticContent.id.toString()
      }
    })

  } catch (error) {
    console.error('Error creating static content:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create static content' },
      { status: 500 }
    )
  }
}

// DELETE /api/static-content - Delete static content
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json(
        { success: false, error: 'Missing id parameter' },
        { status: 400 }
      )
    }

    await prisma.staticcontent.delete({
      where: { id: BigInt(id) }
    })

    return NextResponse.json({
      success: true,
      message: 'Static content deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting static content:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to delete static content' },
      { status: 500 }
    )
  }
}
