import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  ApiError,
  paginatedResponse
} from '@/lib/api-utils'
import { transformFromDbFields } from '@/lib/data-transform'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/admin/chat/threads - Get all chat threads for admin
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { searchParams } = new URL(request.url)
  const page = parseInt(searchParams.get('page') || '1')
  const limit = parseInt(searchParams.get('limit') || '20')
  const search = searchParams.get('search') || ''
  const status = searchParams.get('status') || ''
  const userId = searchParams.get('userId') || ''

  // Get current user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    throw new ApiError('User session required', 401)
  }

  const skip = (page - 1) * limit

  // Build where clause
  const where: any = {
    OR: [
      { messagetype: 'contact' }, // Original contact forms
      { messagetype: 'chat' }, // Chat messages
      { messagetype: 'reply' } // Admin replies
    ]
  }

  // Add search filter
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { subject: { contains: search, mode: 'insensitive' } },
      { message: { contains: search, mode: 'insensitive' } }
    ]
  }

  // Add status filter
  if (status) {
    where.status = status
  }

  // Add user filter
  if (userId) {
    const userIdBigInt = BigInt(userId)
    where.OR = [
      { senderid: userIdBigInt },
      { receiverid: userIdBigInt }
    ]
  }

  // Get unique thread IDs first
  const threadsData = await prisma.contactforms.findMany({
    where,
    select: {
      id: true,
      threadid: true,
      createdat: true
    },
    orderBy: {
      createdat: 'desc'
    }
  })

  // Group by thread ID and get the latest message for each thread
  const threadMap = new Map()
  threadsData.forEach(item => {
    const threadId = item.threadid || item.id
    const threadKey = threadId.toString()
    
    if (!threadMap.has(threadKey) || item.createdat > threadMap.get(threadKey).createdat) {
      threadMap.set(threadKey, {
        threadId,
        latestMessageDate: item.createdat,
        originalId: item.id
      })
    }
  })

  // Get paginated thread IDs
  const allThreads = Array.from(threadMap.values())
    .sort((a, b) => b.latestMessageDate.getTime() - a.latestMessageDate.getTime())
  
  const total = allThreads.length
  const paginatedThreads = allThreads.slice(skip, skip + limit)

  // Get detailed thread information
  const threads = await Promise.all(
    paginatedThreads.map(async (thread) => {
      // Get the original contact form (thread starter)
      const originalMessage = await prisma.contactforms.findFirst({
        where: {
          OR: [
            { id: thread.threadId },
            { threadid: thread.threadId, messagetype: 'contact' }
          ]
        },
        include: {
          sender: {
            select: {
              id: true,
              email: true,
              firstname: true,
              lastname: true,
              imageurl: true,
              role: true
            }
          },
          receiver: {
            select: {
              id: true,
              email: true,
              firstname: true,
              lastname: true,
              imageurl: true,
              role: true
            }
          }
        },
        orderBy: {
          createdat: 'asc'
        }
      })

      // Get the latest message in the thread
      const latestMessage = await prisma.contactforms.findFirst({
        where: {
          OR: [
            { id: thread.threadId },
            { threadid: thread.threadId }
          ]
        },
        include: {
          sender: {
            select: {
              id: true,
              email: true,
              firstname: true,
              lastname: true,
              imageurl: true,
              role: true
            }
          },
          receiver: {
            select: {
              id: true,
              email: true,
              firstname: true,
              lastname: true,
              imageurl: true,
              role: true
            }
          }
        },
        orderBy: {
          createdat: 'desc'
        }
      })

      // Get message count in thread
      const messageCount = await prisma.contactforms.count({
        where: {
          OR: [
            { id: thread.threadId },
            { threadid: thread.threadId }
          ]
        }
      })

      // Get unread count for admin
      const unreadCount = await prisma.contactforms.count({
        where: {
          OR: [
            { id: thread.threadId },
            { threadid: thread.threadId }
          ],
          isread: false,
          receiverid: BigInt(session.user.id)
        }
      })

      return {
        threadId: thread.threadId.toString(),
        originalMessage: originalMessage ? {
          ...transformFromDbFields.contactForm(originalMessage),
          sender: originalMessage.sender,
          receiver: originalMessage.receiver
        } : null,
        latestMessage: latestMessage ? {
          ...transformFromDbFields.contactForm(latestMessage),
          sender: latestMessage.sender,
          receiver: latestMessage.receiver
        } : null,
        messageCount,
        unreadCount,
        lastActivity: thread.latestMessageDate
      }
    })
  )

  return paginatedResponse(threads, page, limit, total)
})
