import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

interface RouteParams {
  params: Promise<{ id: string }>
}

// PUT /api/admin/settings/[id] - Update a site setting
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = await params
    const body = await request.json()

    // Validate required fields
    if (!body.value) {
      return NextResponse.json(
        { success: false, error: 'Value is required' },
        { status: 400 }
      )
    }

    // Check if site setting exists
    const existingSetting = await prisma.sitesettings.findUnique({
      where: { id: BigInt(id) },
    })

    if (!existingSetting) {
      return NextResponse.json(
        { success: false, error: 'Site setting not found' },
        { status: 404 }
      )
    }

    // Update the setting
    const siteSetting = await prisma.sitesettings.update({
      where: { id: BigInt(id) },
      data: {
        value: body.value,
        description: body.description || existingSetting.description,
        updatedat: new Date(),
      }
    })

    // Convert BigInt to string for JSON serialization
    const serializedSetting = {
      ...siteSetting,
      id: siteSetting.id.toString(),
      createdat: siteSetting.createdat?.toISOString(),
      updatedat: siteSetting.updatedat?.toISOString(),
    }

    return NextResponse.json({
      success: true,
      data: serializedSetting,
      message: 'Site setting updated successfully'
    })

  } catch (error) {
    console.error('Error updating site setting:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
