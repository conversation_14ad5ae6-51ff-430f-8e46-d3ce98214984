import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/settings/[id] - Get a specific site setting
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const siteSetting = await prisma.sitesettings.findUnique({
    where: { id }
  })

  if (!siteSetting) {
    throw new ApiError('Site setting not found', 404)
  }

  return successResponse(siteSetting)
})

// PUT /api/admin/settings/[id] - Update a site setting
export const PUT = with<PERSON><PERSON>r<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params
  const validate = validateRequest(schemas.siteSetting.update)
  const data = await validate(request)

  // Check if site setting exists
  const existingSetting = await prisma.sitesettings.findUnique({
    where: { id },
  })

  if (!existingSetting) {
    throw new ApiError('Site setting not found', 404)
  }

  const siteSetting = await prisma.sitesettings.update({
    where: { id },
    data
  })

  return successResponse(siteSetting, 'Site setting updated successfully')
})

// DELETE /api/admin/settings/[id] - Delete a site setting
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const { id } = await params

  // Check if site setting exists
  const existingSetting = await prisma.sitesettings.findUnique({
    where: { id },
  })

  if (!existingSetting) {
    throw new ApiError('Site setting not found', 404)
  }

  await prisma.sitesettings.delete({
    where: { id }
  })

  return successResponse(null, 'Site setting deleted successfully')
})
