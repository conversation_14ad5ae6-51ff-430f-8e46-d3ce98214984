import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

// GET /api/admin/settings/categories - Get all unique categories from settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get unique categories from sitesettings
    const categories = await prisma.sitesettings.findMany({
      select: {
        category: true,
      },
      distinct: ['category'],
      orderBy: {
        category: 'asc'
      }
    })

    const categoryList = categories.map(c => c.category)

    return NextResponse.json({
      success: true,
      data: categoryList,
      message: 'Categories retrieved successfully'
    })

  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/admin/settings/categories - Create a new category (by creating a placeholder setting)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    if (!body.name || !body.label) {
      return NextResponse.json(
        { success: false, error: 'Category name and label are required' },
        { status: 400 }
      )
    }

    const categoryName = body.name.toUpperCase().replace(/\s+/g, '_')

    // Check if category already exists
    const existingCategory = await prisma.sitesettings.findFirst({
      where: { category: categoryName }
    })

    if (existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Category already exists' },
        { status: 400 }
      )
    }

    // Create a placeholder setting for the new category
    const placeholderSetting = await prisma.sitesettings.create({
      data: {
        key: `${categoryName}_CATEGORY_INFO`,
        value: JSON.stringify({
          label: body.label,
          description: body.description || `Settings for ${body.label}`,
          color: body.color || 'gray',
          icon: 'DocumentTextIcon'
        }),
        category: categoryName,
        fieldtype: 'json',
        description: `Category information for ${body.label}`,
        ispublic: false,
        isactive: true,
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        name: categoryName,
        label: body.label,
        description: body.description,
        color: body.color
      },
      message: 'Category created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
