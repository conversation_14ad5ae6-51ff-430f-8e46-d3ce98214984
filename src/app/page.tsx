import { Metadata } from 'next';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { HeroSection } from '@/components/home/<USER>';
import { ServicesSection } from '@/components/home/<USER>';
import { ProjectsSection } from '@/components/home/<USER>';
import { TestimonialsSection } from '@/components/home/<USER>';
import { TechnologiesSection } from '@/components/home/<USER>';
import { TeamSection } from '@/components/home/<USER>';
import { BlogSection } from '@/components/home/<USER>';
import { PricingSection } from '@/components/home/<USER>';
import { NewsletterSection } from '@/components/home/<USER>';
import { CTASection } from '@/components/home/<USER>';
import { ContactSection } from '@/components/home/<USER>';
import { ClientLogosSection } from '@/components/home/<USER>';

// Static data that doesn't change often
const stats = [
  { name: 'Projects Delivered', value: '500+', iconName: 'rocket' },
  { name: 'Happy Clients', value: '200+', iconName: 'users' },
  { name: 'Years Experience', value: '10+', iconName: 'chart' },
  { name: 'Team Members', value: '50+', iconName: 'cpu' },
];

const clientLogos = [
  { name: 'Microsoft', logo: '/images/clients/microsoft.svg', url: '#' },
  { name: 'Google', logo: '/images/clients/google.svg', url: '#' },
  { name: 'Amazon', logo: '/images/clients/amazon.svg', url: '#' },
  { name: 'Apple', logo: '/images/clients/apple.svg', url: '#' },
  { name: 'Meta', logo: '/images/clients/meta.svg', url: '#' },
  { name: 'Netflix', logo: '/images/clients/netflix.svg', url: '#' },
];

const pricingPlans = [
  {
    name: 'Startup',
    price: '$5,000',
    period: 'per project',
    description: 'Perfect for small businesses and startups',
    features: [
      'Custom web application',
      'Responsive design',
      'Basic SEO optimization',
      '3 months support',
      'Source code included'
    ],
    popular: false
  },
  {
    name: 'Business',
    price: '$15,000',
    period: 'per project',
    description: 'Ideal for growing businesses',
    features: [
      'Full-stack application',
      'Advanced UI/UX design',
      'Database integration',
      'API development',
      '6 months support',
      'Performance optimization'
    ],
    popular: true
  },
  {
    name: 'Enterprise',
    price: 'Custom',
    period: 'quote',
    description: 'For large-scale applications',
    features: [
      'Scalable architecture',
      'Microservices design',
      'Cloud deployment',
      'Security audit',
      '12 months support',
      'Dedicated team'
    ],
    popular: false
  }
];

// Fetch data at build time for SSG
async function getHomePageData() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    
    const [servicesRes, projectsRes, testimonialsRes, technologiesRes, teamRes, blogRes] = await Promise.all([
      fetch(`${baseUrl}/api/services?limit=6&filter=active`, { next: { revalidate: 3600 } }),
      fetch(`${baseUrl}/api/projects?featured=true&limit=3`, { next: { revalidate: 3600 } }),
      fetch(`${baseUrl}/api/testimonials?limit=5`, { next: { revalidate: 3600 } }),
      fetch(`${baseUrl}/api/technologies?filter=active&limit=8`, { next: { revalidate: 3600 } }),
      fetch(`${baseUrl}/api/team?filter=active&limit=4`, { next: { revalidate: 3600 } }),
      fetch(`${baseUrl}/api/blog?filter=published&limit=3`, { next: { revalidate: 3600 } }),
    ]);

    const [services, projects, testimonials, technologies, teamMembers, blogPosts] = await Promise.all([
      servicesRes.ok ? servicesRes.json().then(r => r.data || []) : [],
      projectsRes.ok ? projectsRes.json().then(r => r.data || []) : [],
      testimonialsRes.ok ? testimonialsRes.json().then(r => r.data || []) : [],
      technologiesRes.ok ? technologiesRes.json().then(r => r.data || []) : [],
      teamRes.ok ? teamRes.json().then(r => r.data || []) : [],
      blogRes.ok ? blogRes.json().then(r => r.data || []) : [],
    ]);

    return {
      services,
      projects,
      testimonials,
      technologies,
      teamMembers,
      blogPosts,
    };
  } catch (error) {
    console.error('Failed to fetch homepage data:', error);
    return {
      services: [],
      projects: [],
      testimonials: [],
      technologies: [],
      teamMembers: [],
      blogPosts: [],
    };
  }
}

export const metadata: Metadata = {
  title: 'Technoloway - Leading Software Development Company',
  description: 'Transform your ideas into powerful software solutions. We deliver cutting-edge web applications, mobile apps, and enterprise solutions that drive business growth.',
  keywords: ['software development', 'web development', 'mobile apps', 'enterprise solutions', 'TypeScript', 'React', 'Next.js'],
  openGraph: {
    title: 'Technoloway - Leading Software Development Company',
    description: 'Transform your ideas into powerful software solutions. We deliver cutting-edge web applications, mobile apps, and enterprise solutions that drive business growth.',
    images: ['/images/og-homepage.svg'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Technoloway - Leading Software Development Company',
    description: 'Transform your ideas into powerful software solutions. We deliver cutting-edge web applications, mobile apps, and enterprise solutions that drive business growth.',
    images: ['/images/og-homepage.svg'],
  },
};

export default async function HomePage() {
  const data = await getHomePageData();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main>
        <HeroSection stats={stats} />
        <ClientLogosSection logos={clientLogos} />
        <ServicesSection services={data.services} />
        <ProjectsSection projects={data.projects} />
        <TestimonialsSection testimonials={data.testimonials} />
        <TechnologiesSection technologies={data.technologies} />
        <TeamSection teamMembers={data.teamMembers} />
        <BlogSection blogPosts={data.blogPosts} />
        <PricingSection plans={pricingPlans} />
        <NewsletterSection />
        <CTASection />
        <ContactSection />
      </main>
      
      <Footer />
    </div>
  );
}

// Enable static generation with revalidation
export const revalidate = 3600; // Revalidate every hour
