import { NextRequest } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ApiError } from '@/lib/api-utils'

export interface ChatUser {
  id: string | number
  email: string
  role: string
  firstname?: string
  lastname?: string
}

export interface ChatAccessContext {
  user: ChatUser
  contactFormId: number
  targetUserId?: number
}

/**
 * Get authenticated user from session
 */
export async function getAuthenticatedUser(request: NextRequest): Promise<ChatUser> {
  const session = await getServerSession(authOptions)
  
  if (!session?.user?.id) {
    throw new ApiError('Authentication required', 401)
  }

  // Get full user details from database
  const user = await prisma.users.findUnique({
    where: { id: BigInt(session.user.id) },
    select: {
      id: true,
      email: true,
      role: true,
      firstname: true,
      lastname: true
    }
  })

  if (!user) {
    throw new ApiError('User not found', 404)
  }

  return {
    id: user.id.toString(),
    email: user.email,
    role: user.role,
    firstname: user.firstname || undefined,
    lastname: user.lastname || undefined
  }
}

/**
 * Check if user can access a specific contact form for chat
 */
export async function canAccessContactForm(
  user: ChatUser, 
  contactFormId: number
): Promise<boolean> {
  // Admins can access all contact forms
  if (user.role === 'ADMIN') {
    return true
  }

  // Get the contact form
  const contactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(contactFormId) },
    select: {
      id: true,
      email: true,
      userid: true
    }
  })

  if (!contactForm) {
    return false
  }

  // Users can access contact forms they created or that are associated with their email
  if (user.role === 'USER' || user.role === 'CLIENT') {
    // Check if user created this contact form
    if (contactForm.userid && contactForm.userid.toString() === user.id.toString()) {
      return true
    }
    
    // Check if contact form email matches user email
    if (contactForm.email === user.email) {
      return true
    }
  }

  return false
}

/**
 * Check if user can send messages to a specific target user
 */
export async function canMessageUser(
  senderUser: ChatUser,
  targetUserId: number
): Promise<boolean> {
  // Get target user details
  const targetUser = await prisma.users.findUnique({
    where: { id: BigInt(targetUserId) },
    select: {
      id: true,
      role: true
    }
  })

  if (!targetUser) {
    return false
  }

  const senderRole = senderUser.role
  const targetRole = targetUser.role

  // Define allowed messaging relationships
  const allowedRelationships = [
    // Admins can message anyone
    { sender: 'ADMIN', target: 'ADMIN' },
    { sender: 'ADMIN', target: 'USER' },
    { sender: 'ADMIN', target: 'CLIENT' },
    
    // Users can message admins
    { sender: 'USER', target: 'ADMIN' },
    
    // Clients can message admins
    { sender: 'CLIENT', target: 'ADMIN' },
    
    // Users and clients can message back if they received a message first
    { sender: 'USER', target: 'USER' },
    { sender: 'CLIENT', target: 'CLIENT' },
  ]

  return allowedRelationships.some(
    rel => rel.sender === senderRole && rel.target === targetRole
  )
}

/**
 * Get allowed recipient users for a sender in the context of a contact form
 */
export async function getAllowedRecipients(
  senderUser: ChatUser,
  contactFormId: number
): Promise<ChatUser[]> {
  // Get the contact form to understand the context
  const contactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(contactFormId) },
    select: {
      id: true,
      email: true,
      userid: true
    }
  })

  if (!contactForm) {
    return []
  }

  const recipients: ChatUser[] = []

  if (senderUser.role === 'ADMIN') {
    // Admins can message the contact form creator and any related users
    if (contactForm.userid) {
      const contactFormUser = await prisma.users.findUnique({
        where: { id: contactForm.userid },
        select: {
          id: true,
          email: true,
          role: true,
          firstname: true,
          lastname: true
        }
      })
      
      if (contactFormUser) {
        recipients.push({
          id: contactFormUser.id.toString(),
          email: contactFormUser.email,
          role: contactFormUser.role,
          firstname: contactFormUser.firstname || undefined,
          lastname: contactFormUser.lastname || undefined
        })
      }
    }

    // Also include other admins
    const otherAdmins = await prisma.users.findMany({
      where: {
        role: 'ADMIN',
        id: { not: BigInt(senderUser.id) }
      },
      select: {
        id: true,
        email: true,
        role: true,
        firstname: true,
        lastname: true
      }
    })

    otherAdmins.forEach(admin => {
      recipients.push({
        id: admin.id.toString(),
        email: admin.email,
        role: admin.role,
        firstname: admin.firstname || undefined,
        lastname: admin.lastname || undefined
      })
    })

  } else {
    // Non-admins can only message admins
    const admins = await prisma.users.findMany({
      where: { role: 'ADMIN' },
      select: {
        id: true,
        email: true,
        role: true,
        firstname: true,
        lastname: true
      }
    })

    admins.forEach(admin => {
      recipients.push({
        id: admin.id.toString(),
        email: admin.email,
        role: admin.role,
        firstname: admin.firstname || undefined,
        lastname: admin.lastname || undefined
      })
    })
  }

  return recipients
}

/**
 * Validate chat access for API endpoints
 */
export async function validateChatAccess(
  request: NextRequest,
  contactFormId: number,
  targetUserId?: number
): Promise<ChatAccessContext> {
  const user = await getAuthenticatedUser(request)
  
  // Check if user can access this contact form
  const canAccess = await canAccessContactForm(user, contactFormId)
  if (!canAccess) {
    throw new ApiError('Access denied to this contact form', 403)
  }

  // If targeting a specific user, check messaging permissions
  if (targetUserId) {
    const canMessage = await canMessageUser(user, targetUserId)
    if (!canMessage) {
      throw new ApiError('Cannot send messages to this user', 403)
    }
  }

  return {
    user,
    contactFormId,
    targetUserId
  }
}

/**
 * Filter messages based on user access rights
 */
export function filterMessagesForUser(messages: any[], user: ChatUser): any[] {
  // Admins can see all messages
  if (user.role === 'ADMIN') {
    return messages
  }

  // Non-admins can only see messages they sent or received
  return messages.filter(message => {
    const senderId = message.senderid?.toString()
    const receiverId = message.receiverid?.toString()
    const userId = user.id.toString()
    
    return senderId === userId || receiverId === userId || message.email === user.email
  })
}
