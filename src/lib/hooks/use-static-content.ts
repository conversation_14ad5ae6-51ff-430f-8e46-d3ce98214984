'use client'

import { useState, useEffect } from 'react'

interface StaticContentItem {
  id: string
  content: string
  contenttype: string
  displayorder: number
}

interface StaticContentData {
  [page: string]: {
    [section: string]: {
      [contentkey: string]: StaticContentItem
    }
  }
}

interface UseStaticContentReturn {
  data: StaticContentData | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  getContent: (page: string, section: string, contentkey: string, fallback?: string) => string
  updateContent: (updates: Array<{
    page: string
    section: string
    contentkey: string
    content: string
    contenttype?: string
  }>) => Promise<boolean>
}

export function useStaticContent(page?: string, section?: string): UseStaticContentReturn {
  const [data, setData] = useState<StaticContentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchContent = async (retryCount = 0) => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (page) params.append('page', page)
      if (section) params.append('section', section)

      const response = await fetch(`/api/static-content?${params.toString()}`, {
        // Add timeout and retry logic
        signal: AbortSignal.timeout(10000)
      })

      if (!response.ok) {
        throw new Error('Failed to fetch static content')
      }

      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        throw new Error(result.error || 'Failed to fetch static content')
      }
    } catch (err) {
      // Retry logic for connection errors
      if (retryCount < 2 && (err instanceof Error && (err.message.includes('ECONNREFUSED') || err.message.includes('fetch failed')))) {
        console.warn(`Retrying static content fetch (attempt ${retryCount + 1})...`)
        setTimeout(() => fetchContent(retryCount + 1), 1000 * (retryCount + 1))
        return
      }

      console.error('Error fetching static content:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const getContent = (page: string, section: string, contentkey: string, fallback: string = ''): string => {
    if (!data || !data[page] || !data[page][section] || !data[page][section][contentkey]) {
      return fallback
    }
    return data[page][section][contentkey].content
  }

  const updateContent = async (updates: Array<{
    page: string
    section: string
    contentkey: string
    content: string
    contenttype?: string
  }>): Promise<boolean> => {
    try {
      const response = await fetch('/api/static-content', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ updates })
      })

      if (!response.ok) {
        throw new Error('Failed to update static content')
      }

      const result = await response.json()
      
      if (result.success) {
        // Refetch data to get updated content
        await fetchContent()
        return true
      } else {
        throw new Error(result.error || 'Failed to update static content')
      }
    } catch (err) {
      console.error('Error updating static content:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
      return false
    }
  }

  useEffect(() => {
    fetchContent()
  }, [page, section])

  return {
    data,
    loading,
    error,
    refetch: () => fetchContent(),
    getContent,
    updateContent
  }
}

// Helper hook for a specific page/section
export function usePageContent(page: string, section?: string) {
  return useStaticContent(page, section)
}

// Helper function to get content synchronously (for SSR)
export async function getStaticContent(page?: string, section?: string): Promise<StaticContentData> {
  // Skip SSR fetch in development to avoid connection issues
  if (process.env.NODE_ENV === 'development') {
    return {}
  }

  try {
    const params = new URLSearchParams()
    if (page) params.append('page', page)
    if (section) params.append('section', section)

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'
    const response = await fetch(`${baseUrl}/api/static-content?${params.toString()}`, {
      // Add timeout to prevent hanging
      signal: AbortSignal.timeout(5000)
    })

    if (!response.ok) {
      throw new Error('Failed to fetch static content')
    }

    const result = await response.json()

    if (result.success) {
      return result.data
    } else {
      throw new Error(result.error || 'Failed to fetch static content')
    }
  } catch (error) {
    console.error('Error fetching static content:', error)
    return {}
  }
}

// Helper function to get a single content item
export function getContentItem(
  data: StaticContentData | null, 
  page: string, 
  section: string, 
  contentkey: string, 
  fallback: string = ''
): string {
  if (!data || !data[page] || !data[page][section] || !data[page][section][contentkey]) {
    return fallback
  }
  return data[page][section][contentkey].content
}
