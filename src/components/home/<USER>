'use client';

import { useState } from 'react';
import { useStaticContent } from '@/lib/hooks/use-static-content';

export function NewsletterSection() {
  const [isNewsletterSubmitted, setIsNewsletterSubmitted] = useState(false);
  const { getContent } = useStaticContent('home', 'newsletter');

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsNewsletterSubmitted(true);
    setTimeout(() => setIsNewsletterSubmitted(false), 3000);
  };

  return (
    <section className="py-24 bg-white">
      <div className="container px-6 mx-auto">
        <div className="section-header max-w-4xl mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            {getContent('home', 'newsletter', 'title', 'Stay in the')} <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">{getContent('home', 'newsletter', 'title_highlight', 'Loop')}</span>
          </h2>
          <p className="text-xl text-gray-600 mb-12 max-w-2xl mx-auto">
            {getContent('home', 'newsletter', 'subtitle', 'Get the latest insights, tips, and updates delivered straight to your inbox. Join thousands of developers and business leaders.')}
          </p>

          <form onSubmit={handleNewsletterSubmit} className="max-w-md mx-auto">
            <div className="flex flex-col sm:flex-row gap-4">
              <input
                type="email"
                placeholder={getContent('home', 'newsletter', 'input_placeholder', 'Enter your email')}
                required
                className="flex-1 px-6 py-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              <button
                type="submit"
                disabled={isNewsletterSubmitted}
                className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isNewsletterSubmitted ? getContent('home', 'newsletter', 'button_text_success', 'Subscribed!') : getContent('home', 'newsletter', 'button_text', 'Subscribe')}
              </button>
            </div>
            <p className="text-sm text-gray-500 mt-4">
              {getContent('home', 'newsletter', 'privacy_text', 'No spam, unsubscribe at any time. We respect your privacy.')}
            </p>
          </form>
        </div>
      </div>
    </section>
  );
}
