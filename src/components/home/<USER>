import Image from 'next/image';

interface Technology {
  id: string;
  name: string;
  category?: string;
  iconUrl?: string;
  proficiencyLevel?: number;
}

interface TechnologiesSectionProps {
  technologies: Technology[];
}

export function TechnologiesSection({ technologies }: TechnologiesSectionProps) {
  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 via-purple-50/20 to-white">
      <div className="container px-6 mx-auto">
        <div className="section-header text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Our <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Tech Stack</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We use cutting-edge technologies to build scalable, maintainable solutions
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
          {technologies.length > 0 ? (
            technologies.map((tech, index) => (
              <div
                key={tech.id}
                className="service-card group relative"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-white p-6 rounded-2xl border border-gray-200 hover:border-[#d0ebff] hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer">
                  {tech.iconUrl ? (
                    <div className="w-12 h-12 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                      <Image
                        src={tech.iconUrl}
                        alt={tech.name}
                        width={48}
                        height={48}
                        className="w-full h-full object-contain"
                        loading="lazy"
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 bg-gradient-to-br from-[#d0ebff]/30 to-[#e0c3fc]/30 rounded-xl flex items-center justify-center mb-3 mx-auto group-hover:scale-110 transition-transform duration-300">
                      <span className="text-blue-700 font-bold text-lg">{tech.name.charAt(0)}</span>
                    </div>
                  )}
                  <h3 className="text-sm font-semibold text-gray-900 text-center">{tech.name}</h3>
                  <p className="text-xs text-gray-500 text-center mt-1">{tech.category}</p>
                  {tech.proficiencyLevel && (
                    <div className="mt-2">
                      <div className="w-full bg-gray-200 rounded-full h-1">
                        <div
                          className="bg-gradient-to-r from-blue-500 to-purple-600 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${tech.proficiencyLevel}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap z-10">
                  {tech.name} - {tech.category}
                  {tech.proficiencyLevel && ` (${tech.proficiencyLevel}%)`}
                </div>
              </div>
            ))
          ) : (
            // Fallback content when no technologies are available
            Array.from({ length: 8 }, (_, index) => (
              <div
                key={index}
                className="service-card group relative"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="bg-white p-6 rounded-2xl border border-gray-200">
                  <div className="w-12 h-12 bg-gradient-to-br from-[#d0ebff]/30 to-[#e0c3fc]/30 rounded-xl flex items-center justify-center mb-3 mx-auto">
                    <span className="text-blue-700 font-bold text-lg">T</span>
                  </div>
                  <h3 className="text-sm font-semibold text-gray-900 text-center">Technology</h3>
                  <p className="text-xs text-gray-500 text-center mt-1">Coming Soon</p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </section>
  );
}
