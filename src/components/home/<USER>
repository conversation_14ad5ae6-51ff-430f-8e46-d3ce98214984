'use client'

import Link from 'next/link';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { useStaticContent } from '@/lib/hooks/use-static-content';

export function CTASection() {
  const { getContent } = useStaticContent('home', 'cta');
  return (
    <section className="py-24 bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
      <div className="absolute inset-0 bg-black/10"></div>
      <div className="container px-6 mx-auto relative z-10">
        <div className="section-header text-center max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            {getContent('home', 'cta', 'title', 'Let\'s Build Something')}
            <br />
            <span className="text-blue-200">{getContent('home', 'cta', 'title_highlight', 'Great Together')}</span>
          </h2>
          <p className="text-xl text-blue-100 mb-12 max-w-2xl mx-auto">
            {getContent('home', 'cta', 'subtitle', 'Ready to transform your ideas into reality? Get in touch and let\'s discuss your next project.')}
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              href={getContent('home', 'cta', 'primary_button_url', '#contact')}
              className="inline-flex items-center px-8 py-4 bg-white text-gray-900 font-semibold rounded-xl hover:bg-gray-100 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl"
            >
              {getContent('home', 'cta', 'primary_button_text', 'Start Your Project')}
              <ArrowRightIcon className="ml-2 h-5 w-5" />
            </Link>
            <Link
              href={getContent('home', 'cta', 'secondary_button_url', '/projects')}
              className="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm text-white font-semibold rounded-xl border border-white/20 hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-1"
            >
              {getContent('home', 'cta', 'secondary_button_text', 'View Our Work')}
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
