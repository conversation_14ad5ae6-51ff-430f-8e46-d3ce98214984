'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  UserIcon,
  DocumentIcon
} from '@heroicons/react/24/outline'
import { TeamMemberModal } from './team-member-modal'
import { TeamMemberAvatar } from './team-member-avatar'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface TeamMember {
  id: string
  name: string
  position: string
  email: string
  phone: string
  hireDate: string
  isActive: boolean
  updatedAt: string
  [key: string]: any
}

interface TeamMembersManagerProps {
  config: CrudConfig<TeamMember>
}

export function TeamMembersManager({ config }: TeamMembersManagerProps) {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedMembers, setSelectedMembers] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingMember, setEditingMember] = useState<TeamMember | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'name', 'position', 'email', 'phone', 'hireDate', 'updatedAt', 'isActive'
  ])
  const [sortBy, setSortBy] = useState('updatedAt')
  const [sortOrder, setSortOrder] = useState('desc')
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Fetch team members
  const fetchTeamMembers = async (preserveFocus = false) => {
    try {
      // Only show full loading for initial load, not for search
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchQuery && { search: debouncedSearchQuery }),
      })

      console.log('Fetching team members with params:', params.toString()) // Debug log

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`)
      if (!response.ok) throw new Error('Failed to fetch team members')

      const data = await response.json()
      console.log('Received team members data:', data) // Debug log

      setTeamMembers(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / (config.pageSize || 10)))
      setError(null) // Clear any previous errors on successful fetch
    } catch (err) {
      console.error('Error fetching team members:', err) // Debug log
      setError(err instanceof Error ? err.message : 'Failed to fetch team members')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== ''
    fetchTeamMembers(isSearching)
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create team member')

      setIsCreateModalOpen(false)
      fetchTeamMembers()
      alert('Team member created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create team member'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      console.log('Updating team member with data:', formData)

      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      console.log('Update response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Update error response:', errorData)
        throw new Error(errorData.error || `Failed to update team member (${response.status})`)
      }

      const result = await response.json()
      console.log('Update success:', result)

      setIsEditModalOpen(false)
      setEditingMember(null)
      fetchTeamMembers()
      alert('Team member updated successfully!')
    } catch (err) {
      console.error('Update error:', err)
      const errorMessage = err instanceof Error ? err.message : 'Failed to update team member'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete team member')
      }

      // Show success message
      setError(null)
      fetchTeamMembers()

      // Optional: Show success notification
      console.log('Team member deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete team member'
      setError(errorMessage)
      console.error('Delete error:', err)
    }
  }

  // Handle individual actions
  const handleAction = async (action: string, item: TeamMember) => {
    const actionKey = `${action}-${item.id}`

    try {
      setActionLoading(actionKey)

      switch (action) {
        case 'view':
          // Open team member details in new tab or modal
          window.open(`/admin/team-members/${item.id}`, '_blank')
          break

        case 'edit':
          setEditingMember(item)
          setIsEditModalOpen(true)
          break

        case 'toggle-status':
          await handleToggleStatus(item.id, !item.isActive)
          break

        case 'delete':
          const deleteAction = config.actions?.find(a => a.action === 'delete')
          const confirmMessage = deleteAction?.confirmationMessage || 'Are you sure you want to delete this team member? This action cannot be undone.'

          if (window.confirm(confirmMessage)) {
            await handleDelete(item.id)
          }
          break

        default:
          console.warn(`Unknown action: ${action}`)
      }
    } finally {
      setActionLoading(null)
    }
  }

  // Toggle team member status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive }),
      })

      if (!response.ok) throw new Error('Failed to update team member status')

      fetchTeamMembers()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update team member status')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  // Handle bulk actions
  const handleBulkAction = async (action: string, memberIds: string[]) => {
    try {
      let endpoint = `/api/admin/${config.endpoint}`
      let method = 'PUT'
      let body: any = { ids: memberIds }

      switch (action) {
        case 'activate':
          body.data = { isActive: true }
          break
        case 'deactivate':
          body.data = { isActive: false }
          break
        case 'delete':
          method = 'DELETE'
          body = { ids: memberIds }
          break
        default:
          throw new Error(`Unknown bulk action: ${action}`)
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${action} team members`)
      }

      const result = await response.json()

      if (result.success) {
        setSelectedMembers([])
        fetchTeamMembers()
        // Show success notification
      } else {
        throw new Error(result.error || `Failed to ${action} team members`)
      }
    } catch (error) {
      console.error(`Error performing bulk ${action}:`, error)
      throw error
    }
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedMembers.length === teamMembers.length) {
      setSelectedMembers([])
    } else {
      setSelectedMembers(teamMembers.map(member => String(member.id)))
    }
  }

  // Handle select individual member
  const handleSelectMember = (memberId: string) => {
    setSelectedMembers(prev =>
      prev.includes(memberId)
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    )
  }

  // View control functions
  const handleSort = (field) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey) => {
    const isVisible = visibleColumns.includes(columnKey)
    if (isVisible) {
      setVisibleColumns(prev => prev.filter(col => col !== columnKey))
    } else {
      setVisibleColumns(prev => [...prev, columnKey])
    }
  }

  const resetViewSettings = () => {
    setVisibleColumns(['name', 'position', 'email', 'phone', 'hireDate', 'updatedAt', 'isActive'])
    setViewMode('list')
    setDisplayDensity('comfortable')
    setSortBy('updatedAt')
    setSortOrder('desc')
  }

  const availableColumns = [
    { key: 'name', label: 'Name', hideable: false },
    { key: 'position', label: 'Position', hideable: true },
    { key: 'email', label: 'Email', hideable: true },
    { key: 'phone', label: 'Phone', hideable: true },
    { key: 'hireDate', label: 'Hire Date', hideable: true },
    { key: 'salary', label: 'Salary', hideable: true },
    { key: 'city', label: 'City', hideable: true },
    { key: 'updatedAt', label: 'Last Active', hideable: true },
    { key: 'isActive', label: 'Status', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add Team Member</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder={config.searchPlaceholder || 'Search team members by name, email, position...'}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchQuery !== debouncedSearchQuery && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'card')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns ({visibleColumns.length}/{availableColumns.filter(col => col.hideable !== false).length})</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px] max-h-64 overflow-y-auto"
                  >
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-100">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Column Visibility
                        </span>
                        <button
                          onClick={() => {
                            resetViewSettings()
                            setShowColumnMenu(false)
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Reset
                        </button>
                      </div>

                      {availableColumns
                        .filter(col => col.hideable !== false)
                        .map((column) => {
                          const isVisible = visibleColumns.includes(column.key)
                          return (
                            <label
                              key={column.key}
                              className="flex items-center space-x-2 py-1 hover:bg-gray-50 rounded cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={isVisible}
                                onChange={() => handleColumnToggle(column.key)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm text-gray-700">{column.label}</span>
                              {isVisible ? (
                                <EyeIcon className="h-3 w-3 text-gray-400" />
                              ) : (
                                <EyeSlashIcon className="h-3 w-3 text-gray-400" />
                              )}
                            </label>
                          )
                        })}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Sort options"
              >
                {sortOrder === 'asc' ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
                <span>Sort</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showSortMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]"
                  >
                    <div className="p-2">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 pb-2 border-b border-gray-100">
                        Sort By
                      </div>
                      {availableColumns.map((column) => (
                        <button
                          key={column.key}
                          onClick={() => {
                            handleSort(column.key)
                            setShowSortMenu(false)
                          }}
                          className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors flex items-center justify-between ${
                            sortBy === column.key
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700'
                          }`}
                        >
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchQuery || showFilters) && (
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-500">Active:</span>
            {searchQuery && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{searchQuery}"
                <button
                  onClick={() => setSearchQuery('')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Sort: {availableColumns.find(col => col.key === sortBy)?.label} ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              View: {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Density: {densityLabels[displayDensity]}
            </span>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Bulk Actions */}
      {config.enableBulkActions && selectedMembers.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="text-sm font-medium text-blue-900">
                {selectedMembers.length} team member{selectedMembers.length !== 1 ? 's' : ''} selected
              </span>
            </div>
            <div className="flex items-center space-x-2">
              {config.bulkActions?.map((action) => (
                <button
                  key={action.action}
                  onClick={() => {
                    if (action.requiresConfirmation) {
                      if (window.confirm(action.confirmationMessage || `Are you sure you want to ${action.action} the selected team members?`)) {
                        handleBulkAction(action.action, selectedMembers)
                      }
                    } else {
                      handleBulkAction(action.action, selectedMembers)
                    }
                  }}
                  className={`inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md ${
                    action.variant === 'danger'
                      ? 'text-red-700 bg-red-100 hover:bg-red-200'
                      : action.variant === 'warning'
                      ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200'
                      : action.variant === 'success'
                      ? 'text-green-700 bg-green-100 hover:bg-green-200'
                      : 'text-blue-700 bg-blue-100 hover:bg-blue-200'
                  }`}
                >
                  {action.label}
                </button>
              ))}
              <button
                onClick={() => setSelectedMembers([])}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Team Members Display */}
      {viewMode === 'list' ? (
        /* Table View */
        <div className="bg-white shadow-sm rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {/* Checkbox column for bulk actions */}
              {config.enableBulkActions && (
                <th className={`px-3 text-left ${displayDensity === 'compact' ? 'py-2' : 'py-3'} w-12`}>
                  <input
                    type="checkbox"
                    checked={selectedMembers.length === teamMembers.length && teamMembers.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
              )}
              {/* Dynamic columns based on visibility settings */}
              {visibleColumns.includes('name') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-48 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Name</span>
                    {sortBy === 'name' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              {visibleColumns.includes('position') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-40 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('position')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Position</span>
                    {sortBy === 'position' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              {visibleColumns.includes('email') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-56 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('email')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Email</span>
                    {sortBy === 'email' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              {visibleColumns.includes('phone') && (
                <th className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-36 ${
                  displayDensity === 'compact' ? 'py-2' : 'py-3'
                }`}>
                  Phone
                </th>
              )}
              {visibleColumns.includes('hireDate') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('hireDate')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Hire Date</span>
                    {sortBy === 'hireDate' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              {visibleColumns.includes('salary') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-28 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('salary')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Salary</span>
                    {sortBy === 'salary' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              {visibleColumns.includes('city') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-28 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('city')}
                >
                  <div className="flex items-center space-x-1">
                    <span>City</span>
                    {sortBy === 'city' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              {visibleColumns.includes('updatedAt') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-32 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('updatedAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Last Active</span>
                    {sortBy === 'updatedAt' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              {visibleColumns.includes('isActive') && (
                <th
                  className={`px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 w-24 ${
                    displayDensity === 'compact' ? 'py-2' : 'py-3'
                  }`}
                  onClick={() => handleSort('isActive')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Status</span>
                    {sortBy === 'isActive' && (
                      sortOrder === 'asc' ? <ArrowUpIcon className="h-3 w-3" /> : <ArrowDownIcon className="h-3 w-3" />
                    )}
                  </div>
                </th>
              )}
              <th className={`px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-28 ${
                displayDensity === 'compact' ? 'py-2' : 'py-3'
              }`}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {teamMembers.map((member) => (
              <tr key={member.id} className={`hover:bg-gray-50 ${selectedMembers.includes(String(member.id)) ? 'bg-blue-50 border-l-4 border-blue-500' : ''}`}>
                {/* Checkbox cell for bulk actions */}
                {config.enableBulkActions && (
                  <td className={`px-3 ${displayDensity === 'compact' ? 'py-2' : 'py-4'} w-12`}>
                    <input
                      type="checkbox"
                      checked={selectedMembers.includes(String(member.id))}
                      onChange={() => handleSelectMember(String(member.id))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </td>
                )}
                {/* Dynamic cells based on visibility settings */}
                {visibleColumns.includes('name') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <div className="font-medium text-gray-900 truncate">{member.name}</div>
                  </td>
                )}
                {visibleColumns.includes('position') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <div className="text-sm text-gray-900 truncate">{member.position}</div>
                  </td>
                )}
                {visibleColumns.includes('email') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <div className="text-sm text-gray-900 truncate">{member.email}</div>
                  </td>
                )}
                {visibleColumns.includes('phone') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <div className="text-sm text-gray-500 truncate">{member.phone}</div>
                  </td>
                )}
                {visibleColumns.includes('hireDate') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {formatDate(member.hireDate)}
                  </td>
                )}
                {visibleColumns.includes('salary') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {member.salary ? `$${member.salary.toLocaleString()}` : '-'}
                  </td>
                )}
                {visibleColumns.includes('city') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {member.city || '-'}
                  </td>
                )}
                {visibleColumns.includes('updatedAt') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm text-gray-900`}>
                    {formatDate(member.updatedAt)}
                  </td>
                )}
                {visibleColumns.includes('isActive') && (
                  <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'}`}>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      member.isActive
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {member.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                )}
                <td className={`px-4 ${displayDensity === 'compact' ? 'py-2' : 'py-3'} text-sm font-medium`}>
                  <div className="flex items-center justify-center space-x-1">
                    {config.actions?.map((action) => {
                      const getActionIcon = (actionType) => {
                        switch (actionType) {
                          case 'edit':
                            return PencilIcon
                          case 'delete':
                            return TrashIcon
                          case 'toggle-status':
                            return PowerIcon
                          case 'view':
                            return UserIcon
                          default:
                            return UserIcon
                        }
                      }

                      const IconComponent = getActionIcon(action.action)

                      return (
                        <button
                          key={action.action}
                          onClick={() => handleAction(action.action, member)}
                          disabled={actionLoading === `${action.action}-${member.id}`}
                          className={`${displayDensity === 'compact' ? 'p-1.5' : 'p-2'} rounded-md transition-all duration-200 border border-transparent hover:border-solid ${
                            action.variant === 'primary' ? 'text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-200' :
                            action.variant === 'warning' ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50 hover:border-orange-200' :
                            action.variant === 'danger' ? 'text-red-600 hover:text-red-800 hover:bg-red-50 hover:border-red-200' :
                            'text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-200'
                          } ${actionLoading === `${action.action}-${member.id}` ? 'opacity-50 cursor-not-allowed' : ''}`}
                          title={action.tooltip}
                        >
                          {actionLoading === `${action.action}-${member.id}` ? (
                            <div className={`border-2 border-current border-t-transparent rounded-full animate-spin ${
                              displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'
                            }`} />
                          ) : (
                            <IconComponent className={displayDensity === 'compact' ? 'w-3 h-3' : 'w-4 h-4'} />
                          )}
                        </button>
                      )
                    })}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
          </div>
        </div>
      ) : viewMode === 'grid' ? (
        /* Grid View */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-2">
            {teamMembers.map((member) => (
              <div key={member.id} className={`rounded-lg p-4 hover:bg-gray-100 transition-colors border-2 ${selectedMembers.includes(String(member.id)) ? 'bg-blue-50 border-blue-500' : 'bg-gray-50 border-gray-200'}`}>
                <div className="flex items-center space-x-3 mb-3">
                  <TeamMemberAvatar
                    name={member.name}
                    photoUrl={member.photoUrl}
                    size="md"
                  />
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-semibold text-gray-900 truncate">{member.name}</h3>
                    <p className="text-xs text-gray-600 truncate">{member.position}</p>
                  </div>
                  {config.enableBulkActions && (
                    <input
                      type="checkbox"
                      checked={selectedMembers.includes(String(member.id))}
                      onChange={() => handleSelectMember(String(member.id))}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  )}
                </div>
                <div className="space-y-2 text-xs text-gray-600">
                  <div className="flex items-center justify-between">
                    <span>Email:</span>
                    <span className="truncate ml-2">{member.email}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Phone:</span>
                    <span className="truncate ml-2">{member.phone}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Status:</span>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      member.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {member.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-center space-x-1 mt-3 pt-3 border-t border-gray-200">
                  {config.actions?.map((action) => {
                    const getActionIcon = (actionType) => {
                      switch (actionType) {
                        case 'edit': return PencilIcon
                        case 'delete': return TrashIcon
                        case 'toggle-status': return PowerIcon
                        case 'view': return UserIcon
                        default: return UserIcon
                      }
                    }
                    const IconComponent = getActionIcon(action.action)
                    return (
                      <button
                        key={action.action}
                        onClick={() => handleAction(action.action, member)}
                        disabled={actionLoading === `${action.action}-${member.id}`}
                        className={`p-1.5 rounded-md transition-all duration-200 border border-transparent hover:border-solid ${
                          action.variant === 'primary' ? 'text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-200' :
                          action.variant === 'warning' ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50 hover:border-orange-200' :
                          action.variant === 'danger' ? 'text-red-600 hover:text-red-800 hover:bg-red-50 hover:border-red-200' :
                          'text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-200'
                        } ${actionLoading === `${action.action}-${member.id}` ? 'opacity-50 cursor-not-allowed' : ''}`}
                        title={action.tooltip}
                      >
                        {actionLoading === `${action.action}-${member.id}` ? (
                          <div className="border-2 border-current border-t-transparent rounded-full animate-spin w-3 h-3" />
                        ) : (
                          <IconComponent className="w-3 h-3" />
                        )}
                      </button>
                    )
                  })}
                </div>
              </div>
            ))}
        </div>
      ) : (
        /* Card View */
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 px-2">
            {teamMembers.map((member) => (
              <div key={member.id} className={`rounded-2xl overflow-hidden hover:shadow-2xl transition-all duration-300 border-2 min-h-[320px] ${selectedMembers.includes(String(member.id)) ? 'bg-blue-50 border-blue-500' : 'bg-white border-gray-100 hover:border-blue-200'}`}>
                <div className="flex h-full">
                  {/* Photo Section - Full Height */}
                  <div className="flex-shrink-0 w-56 relative">
                    <TeamMemberAvatar
                      name={member.name}
                      photoUrl={member.photoUrl}
                      size="full-height"
                      className="shadow-none"
                      style={{
                        width: '100%',
                        height: '100%',
                        minHeight: '320px'
                      }}
                    />
                    {/* Checkbox Overlay */}
                    {config.enableBulkActions && (
                      <div className="absolute top-4 left-4">
                        <input
                          type="checkbox"
                          checked={selectedMembers.includes(String(member.id))}
                          onChange={() => handleSelectMember(String(member.id))}
                          className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded shadow-lg"
                        />
                      </div>
                    )}
                    {/* Status Badge Overlay */}
                    <div className="absolute top-4 right-4">
                      <span className={`inline-flex px-3 py-1.5 text-xs font-bold rounded-full shadow-lg ${
                        member.isActive ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
                      }`}>
                        {member.isActive ? 'ACTIVE' : 'INACTIVE'}
                      </span>
                    </div>
                  </div>

                  {/* Content Section */}
                  <div className="flex-1 p-8 flex flex-col justify-between min-w-0">
                    {/* Header */}
                    <div>
                      <div className="mb-6">
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">{member.name}</h3>
                        <p className="text-xl text-blue-600 font-semibold">{member.position}</p>
                      </div>

                      {/* Bio Section */}
                      {member.bio && (
                        <div className="mb-6">
                          <p className="text-gray-700 text-sm leading-relaxed italic border-l-4 border-blue-200 pl-4">
                            "{member.bio}"
                          </p>
                        </div>
                      )}

                      {/* Contact Information */}
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm">
                        <div className="space-y-3">
                          <div className="flex items-center">
                            <span className="font-bold text-gray-800 w-16">Email:</span>
                            <span className="text-gray-600 ml-2">{member.email || 'N/A'}</span>
                          </div>
                          <div className="flex items-center">
                            <span className="font-bold text-gray-800 w-16">Phone:</span>
                            <span className="text-gray-600 ml-2">{member.phone}</span>
                          </div>
                        </div>
                        <div className="space-y-3">
                          {member.hireDate && (
                            <div className="flex items-center">
                              <span className="font-bold text-gray-800 w-16">Hired:</span>
                              <span className="text-gray-600 ml-2">{formatDate(member.hireDate)}</span>
                            </div>
                          )}
                          {member.city && (
                            <div className="flex items-center">
                              <span className="font-bold text-gray-800 w-16">City:</span>
                              <span className="text-gray-600 ml-2">{member.city}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Social Links */}
                      {(member.linkedinUrl || member.githubUrl) && (
                        <div className="flex items-center space-x-4 mt-6 pt-4 border-t border-gray-100">
                          <span className="text-sm font-bold text-gray-800">Connect:</span>
                          {member.linkedinUrl && (
                            <a
                              href={member.linkedinUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-full hover:bg-blue-700 transition-colors"
                            >
                              LinkedIn
                            </a>
                          )}
                          {member.githubUrl && (
                            <a
                              href={member.githubUrl}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center px-3 py-1.5 bg-gray-800 text-white text-xs font-medium rounded-full hover:bg-gray-900 transition-colors"
                            >
                              GitHub
                            </a>
                          )}
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex items-center justify-end space-x-3 mt-8 pt-6 border-t border-gray-200">
                      {/* Resume Button */}
                      {member.resumeUrl && (
                        <button
                          onClick={() => window.open(member.resumeUrl, '_blank')}
                          className="p-3 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-solid shadow-md hover:shadow-lg transform hover:-translate-y-0.5 text-green-600 hover:text-green-800 hover:bg-green-50 hover:border-green-300"
                          title="Open Resume"
                        >
                          <DocumentIcon className="w-5 h-5" />
                        </button>
                      )}

                      {/* Standard Action Buttons */}
                      {config.actions?.map((action) => {
                        const getActionIcon = (actionType) => {
                          switch (actionType) {
                            case 'edit': return PencilIcon
                            case 'delete': return TrashIcon
                            case 'toggle-status': return PowerIcon
                            case 'view': return UserIcon
                            default: return UserIcon
                          }
                        }
                        const IconComponent = getActionIcon(action.action)
                        return (
                          <button
                            key={action.action}
                            onClick={() => handleAction(action.action, member)}
                            disabled={actionLoading === `${action.action}-${member.id}`}
                            className={`p-3 rounded-xl transition-all duration-200 border-2 border-transparent hover:border-solid shadow-md hover:shadow-lg transform hover:-translate-y-0.5 ${
                              action.variant === 'primary' ? 'text-blue-600 hover:text-blue-800 hover:bg-blue-50 hover:border-blue-300' :
                              action.variant === 'warning' ? 'text-orange-600 hover:text-orange-800 hover:bg-orange-50 hover:border-orange-300' :
                              action.variant === 'danger' ? 'text-red-600 hover:text-red-800 hover:bg-red-50 hover:border-red-300' :
                              'text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300'
                            } ${actionLoading === `${action.action}-${member.id}` ? 'opacity-50 cursor-not-allowed' : ''}`}
                            title={action.tooltip}
                          >
                            {actionLoading === `${action.action}-${member.id}` ? (
                              <div className="border-2 border-current border-t-transparent rounded-full animate-spin w-5 h-5" />
                            ) : (
                              <IconComponent className="w-5 h-5" />
                            )}
                          </button>
                        )
                      })}
                    </div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow-sm">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        currentPage === page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Create Modal */}
      <TeamMemberModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create Team Member"
        fields={config.fields}
        layout={config.formLayout}
      />

      {/* Edit Modal */}
      <TeamMemberModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingMember(null)
        }}
        onSubmit={(data) => editingMember && handleUpdate(editingMember.id, data)}
        title="Edit Team Member"
        initialData={editingMember}
        fields={config.fields}
        layout={config.formLayout}
      />
    </div>
  )
}
