'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  PaperAirplaneIcon,
  CheckIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline'

interface Invoice {
  id: string
  invoiceNumber: string
  client: {
    companyName: string
    contactName: string
    contactEmail: string
  }
  project?: {
    name: string
    status: string
  }
  totalAmount: number
  status: 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED'
  issueDate: string
  dueDate: string
  paidAt?: string
  items: Array<{
    id: string
    description: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  payments: Array<{
    id: string
    amount: number
    status: string
    paidAt?: string
  }>
}

interface InvoiceCardProps {
  invoice: Invoice
  onView: (invoice: Invoice) => void
  onEdit: (invoice: Invoice) => void
  onDelete: (id: string) => void
  onDownload: (invoice: Invoice) => void
  onSend: (invoice: Invoice) => void
  onMarkPaid: (invoice: Invoice) => void
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return DocumentTextIcon
    case 'SENT':
      return PaperAirplaneIcon
    case 'PAID':
      return CheckIcon
    case 'OVERDUE':
      return ExclamationTriangleIcon
    case 'CANCELLED':
      return TrashIcon
    default:
      return ClockIcon
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'DRAFT':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'SENT':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'PAID':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'OVERDUE':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'CANCELLED':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const getDaysUntilDue = (dueDate: string) => {
  const today = new Date()
  const due = new Date(dueDate)
  const diffTime = due.getTime() - today.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  return diffDays
}

export default function InvoiceCard({
  invoice,
  onView,
  onEdit,
  onDelete,
  onDownload,
  onSend,
  onMarkPaid
}: InvoiceCardProps) {
  const [isLoading, setIsLoading] = useState(false)
  const StatusIcon = getStatusIcon(invoice.status)
  const daysUntilDue = getDaysUntilDue(invoice.dueDate)
  const isOverdue = daysUntilDue < 0 && invoice.status !== 'PAID'
  const totalPaid = invoice.payments.reduce((sum, payment) =>
    payment.status === 'COMPLETED' ? sum + Number(payment.amount) : sum, 0
  )

  const handleAction = async (action: () => void) => {
    setIsLoading(true)
    try {
      await action()
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200"
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center border ${getStatusColor(invoice.status)}`}>
              <StatusIcon className="w-5 h-5" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {invoice.invoiceNumber}
              </h3>
              <p className="text-sm text-gray-600">
                {invoice.client.companyName}
              </p>
              {invoice.project && (
                <p className="text-xs text-gray-500">
                  Project: {invoice.project.name}
                </p>
              )}
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-xl font-bold text-gray-900">
              {formatCurrency(invoice.totalAmount)}
            </div>
            <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
              {invoice.status}
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Issue Date:</span>
            <div className="font-medium">{formatDate(invoice.issueDate)}</div>
          </div>
          <div>
            <span className="text-gray-500">Due Date:</span>
            <div className={`font-medium ${isOverdue ? 'text-red-600' : ''}`}>
              {formatDate(invoice.dueDate)}
              {isOverdue && (
                <span className="ml-1 text-red-500">
                  ({Math.abs(daysUntilDue)} days overdue)
                </span>
              )}
            </div>
          </div>
          <div>
            <span className="text-gray-500">Items:</span>
            <div className="font-medium">{invoice.items.length}</div>
          </div>
          <div>
            <span className="text-gray-500">Payments:</span>
            <div className="font-medium">
              {totalPaid > 0 ? formatCurrency(totalPaid) : 'None'}
            </div>
          </div>
        </div>

        {invoice.status === 'PAID' && invoice.paidAt && (
          <div className="mt-3 p-2 bg-green-50 rounded-md">
            <div className="text-sm text-green-800">
              Paid on {formatDate(invoice.paidAt)}
            </div>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="px-4 py-3 bg-gray-50 rounded-b-lg">
        <div className="flex items-center justify-between">
          <div className="flex space-x-2">
            <button
              onClick={() => handleAction(() => onView(invoice))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-blue-600 transition-colors disabled:opacity-50"
              title="View Details"
            >
              <EyeIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleAction(() => onEdit(invoice))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-blue-600 transition-colors disabled:opacity-50"
              title="Edit"
            >
              <PencilIcon className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleAction(() => onDownload(invoice))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-green-600 transition-colors disabled:opacity-50"
              title="Download PDF"
            >
              <ArrowDownTrayIcon className="h-4 w-4" />
            </button>
          </div>

          <div className="flex space-x-2">
            {invoice.status === 'DRAFT' && (
              <button
                onClick={() => handleAction(() => onSend(invoice))}
                disabled={isLoading}
                className="px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors disabled:opacity-50"
              >
                Send
              </button>
            )}
            {(invoice.status === 'SENT' || invoice.status === 'OVERDUE') && (
              <button
                onClick={() => handleAction(() => onMarkPaid(invoice))}
                disabled={isLoading}
                className="px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200 transition-colors disabled:opacity-50"
              >
                Mark Paid
              </button>
            )}
            <button
              onClick={() => handleAction(() => onDelete(invoice.id))}
              disabled={isLoading}
              className="p-2 text-gray-400 hover:text-red-600 transition-colors disabled:opacity-50"
              title="Delete"
            >
              <TrashIcon className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
