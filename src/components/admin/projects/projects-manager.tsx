'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  EyeIcon,
  EyeSlashIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChevronDownIcon,
  AdjustmentsHorizontalIcon,
  ListBulletIcon,
  Squares2X2Icon,
  RectangleStackIcon,
} from '@heroicons/react/24/outline'
import { CrudConfig } from '@/components/admin/crud/types'
import ProjectModal from './project-modal'

interface Project {
  id: string
  name: string
  description: string
  projgoals?: string
  projmanager?: string
  clientid?: string
  orderid: string
  imageurl?: string
  projecturl?: string
  githuburl?: string
  tags?: string
  projstartdate?: string
  projcompletiondate?: string
  estimatecost?: number
  estimatetime?: string
  estimateeffort?: string
  status?: string
  isfeatured?: boolean
  ispublic?: boolean
  displayorder: number
  createdat: string
  updatedat?: string
  clients?: {
    id: string
    companyname: string
    contactname: string
  }
  teammembers?: {
    id: string
    name: string
  }
  _count?: {
    tasks: number
    projectdocuments: number
    messages: number
  }
  [key: string]: any
}

interface ProjectsManagerProps {
  config: CrudConfig<Project>
}

export function ProjectsManager({ config }: ProjectsManagerProps) {
  // State management
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProjects, setSelectedProjects] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingProject, setEditingProject] = useState<Project | null>(null)

  // Search input ref for focus management
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'name', 'client', 'status', 'budget', 'timeline', 'flags', 'updatedat'
  ])
  const [sortBy, setSortBy] = useState('updatedat')
  const [sortOrder, setSortOrder] = useState('desc')
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Reset to first page when search or filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchTerm])

  // Fetch projects
  const fetchProjects = async (preserveFocus = false) => {
    try {
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
      })

      const response = await fetch(`/api/admin/projects?${params}`)
      if (!response.ok) throw new Error('Failed to fetch projects')

      const data = await response.json()
      console.log('Projects API Response:', data) // Debug log
      setProjects(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / 10))
      setError(null)
    } catch (err) {
      console.error('Error fetching projects:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch projects')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const isSearching = debouncedSearchTerm !== ''
    fetchProjects(isSearching)
  }, [currentPage, debouncedSearchTerm, sortBy, sortOrder])

  // Handle bulk selection
  const handleSelectAll = () => {
    if (selectedProjects.length === projects.length) {
      setSelectedProjects([])
    } else {
      setSelectedProjects(projects.map(project => project.id.toString()))
    }
  }

  const handleSelectProject = (id: string) => {
    setSelectedProjects(prev =>
      prev.includes(id)
        ? prev.filter(projectId => projectId !== id)
        : [...prev, id]
    )
  }

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to create project')

      setIsCreateModalOpen(false)
      fetchProjects()
      alert('Project created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create project'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/projects/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      
      if (!response.ok) throw new Error('Failed to update project')

      setIsEditModalOpen(false)
      setEditingProject(null)
      fetchProjects()
      alert('Project updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update project'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/projects/${id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      })

      if (!response.ok) throw new Error('Failed to delete project')

      setError(null)
      fetchProjects()
      console.log('Project deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete project'
      setError(errorMessage)
      console.error('Delete error:', err)
    }
  }

  // Toggle project status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/projects/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ispublic: isActive }),
      })

      if (!response.ok) throw new Error('Failed to update project status')

      fetchProjects()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update project status')
    }
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  // View control functions
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey: string) => {
    const isVisible = visibleColumns.includes(columnKey)
    if (isVisible) {
      setVisibleColumns(prev => prev.filter(col => col !== columnKey))
    } else {
      setVisibleColumns(prev => [...prev, columnKey])
    }
  }

  const resetViewSettings = () => {
    setVisibleColumns(['name', 'client', 'status', 'budget', 'timeline', 'flags', 'updatedat'])
    setViewMode('list')
    setDisplayDensity('comfortable')
    setSortBy('updatedat')
    setSortOrder('desc')
  }

  const availableColumns = [
    { key: 'name', label: 'Project Name', hideable: false },
    { key: 'client', label: 'Client', hideable: true },
    { key: 'status', label: 'Status', hideable: true },
    { key: 'budget', label: 'Budget', hideable: true },
    { key: 'timeline', label: 'Timeline', hideable: true },
    { key: 'flags', label: 'Flags', hideable: true },
    { key: 'manager', label: 'Manager', hideable: true },
    { key: 'updatedat', label: 'Last Updated', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  // Get density-based classes
  const getDensityClasses = () => {
    return displayDensity === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-4'
  }

  const getHeaderDensityClasses = () => {
    return displayDensity === 'compact'
      ? 'px-4 py-2'
      : 'px-6 py-3'
  }

  const getImageSize = () => {
    return displayDensity === 'compact'
      ? 'h-8 w-8'
      : 'h-10 w-10'
  }

  const getTextSize = () => {
    return displayDensity === 'compact'
      ? 'text-xs'
      : 'text-sm'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add Project</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search projects by name, description, client..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchQuery !== debouncedSearchTerm && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'card')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns ({visibleColumns.length}/{availableColumns.filter(col => col.hideable !== false).length})</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px] max-h-64 overflow-y-auto"
                  >
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-100">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Column Visibility
                        </span>
                        <button
                          onClick={() => {
                            resetViewSettings()
                            setShowColumnMenu(false)
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Reset
                        </button>
                      </div>

                      {availableColumns
                        .filter(col => col.hideable !== false)
                        .map((column) => {
                          const isVisible = visibleColumns.includes(column.key)
                          return (
                            <label
                              key={column.key}
                              className="flex items-center space-x-2 py-1 hover:bg-gray-50 rounded cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={isVisible}
                                onChange={() => handleColumnToggle(column.key)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm text-gray-700">{column.label}</span>
                              {isVisible ? (
                                <EyeIcon className="h-3 w-3 text-gray-400" />
                              ) : (
                                <EyeSlashIcon className="h-3 w-3 text-gray-400" />
                              )}
                            </label>
                          )
                        })}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Sort options"
              >
                {sortOrder === 'asc' ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
                <span>Sort</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showSortMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]"
                  >
                    <div className="p-2">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 pb-2 border-b border-gray-100">
                        Sort By
                      </div>
                      {availableColumns.map((column) => (
                        <button
                          key={column.key}
                          onClick={() => {
                            handleSort(column.key)
                            setShowSortMenu(false)
                          }}
                          className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors flex items-center justify-between ${
                            sortBy === column.key
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700'
                          }`}
                        >
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchQuery || showFilters) && (
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-500">Active:</span>
            {searchQuery && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{searchQuery}"
                <button
                  onClick={() => setSearchQuery('')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Sort: {availableColumns.find(col => col.key === sortBy)?.label} ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              View: {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Density: {densityLabels[displayDensity]}
            </span>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Projects Display */}
      {viewMode === 'list' ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className={`w-12 ${getHeaderDensityClasses()} text-left`}>
                  <input
                    type="checkbox"
                    checked={selectedProjects.length === projects.length && projects.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>

                {availableColumns
                  .filter(column => visibleColumns.includes(column.key))
                  .map((column) => (
                    <th
                      key={column.key}
                      className={`${getHeaderDensityClasses()} text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100`}
                      onClick={() => handleSort(column.key)}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{column.label}</span>
                        {sortBy === column.key && (
                          sortOrder === 'asc' ? (
                            <ArrowUpIcon className="h-3 w-3" />
                          ) : (
                            <ArrowDownIcon className="h-3 w-3" />
                          )
                        )}
                      </div>
                    </th>
                  ))}

                <th className={`${getHeaderDensityClasses()} text-right text-xs font-medium text-gray-500 uppercase tracking-wider`}>
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {projects.length === 0 ? (
                <tr>
                  <td colSpan={availableColumns.filter(col => visibleColumns.includes(col.key)).length + 2} className="px-6 py-12 text-center">
                    <div className="text-gray-500">
                      {debouncedSearchTerm ? 'No projects found matching your search.' : 'No projects found.'}
                    </div>
                  </td>
                </tr>
              ) : (
                projects.map((project) => (
                  <tr
                    key={project.id}
                    className={`hover:bg-gray-50 ${
                      selectedProjects.includes(project.id.toString()) ? 'bg-blue-50' : ''
                    }`}
                  >
                    <td className={`${getDensityClasses()} whitespace-nowrap`}>
                      <input
                        type="checkbox"
                        checked={selectedProjects.includes(project.id.toString())}
                        onChange={() => handleSelectProject(project.id.toString())}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>

                    {availableColumns
                      .filter(column => visibleColumns.includes(column.key))
                      .map((column) => (
                        <td key={column.key} className={`${getDensityClasses()} whitespace-nowrap`}>
                          {column.key === 'name' && (
                            <div className="flex items-center">
                              <div className={`flex-shrink-0 ${getImageSize()}`}>
                                {project.imageurl ? (
                                  <img
                                    className={`${getImageSize()} rounded-lg object-cover`}
                                    src={project.imageurl}
                                    alt={project.name}
                                  />
                                ) : (
                                  <div className={`${getImageSize()} rounded-lg bg-gray-200 flex items-center justify-center`}>
                                    <span className={`text-gray-500 ${getTextSize()} font-medium`}>
                                      {project.name.charAt(0)}
                                    </span>
                                  </div>
                                )}
                              </div>
                              <div className={displayDensity === 'compact' ? 'ml-2' : 'ml-4'}>
                                <div className={`${getTextSize()} font-medium text-gray-900`}>{project.name}</div>
                                {displayDensity === 'comfortable' && (
                                  <div className="text-sm text-gray-500">{project.description?.substring(0, 50)}...</div>
                                )}
                              </div>
                            </div>
                          )}
                          {column.key === 'client' && (
                            <div className={`${getTextSize()} text-gray-900`}>
                              {project.clients?.companyname || 'No client'}
                            </div>
                          )}
                          {column.key === 'status' && (
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              project.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                              project.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                              project.status === 'PLANNING' ? 'bg-yellow-100 text-yellow-800' :
                              project.status === 'ON_HOLD' ? 'bg-gray-100 text-gray-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {project.status || 'Unknown'}
                            </span>
                          )}
                          {column.key === 'budget' && (
                            <div className={`${getTextSize()} text-gray-900`}>
                              {project.estimatecost ? formatCurrency(project.estimatecost) : 'N/A'}
                            </div>
                          )}
                          {column.key === 'timeline' && (
                            <div className={`${getTextSize()} text-gray-900`}>
                              <div>{project.projstartdate ? formatDate(project.projstartdate) : 'TBD'}</div>
                              {displayDensity === 'comfortable' && (
                                <div className="text-xs text-gray-500">
                                  {project.projcompletiondate ? formatDate(project.projcompletiondate) : 'TBD'}
                                </div>
                              )}
                            </div>
                          )}
                          {column.key === 'flags' && (
                            <div className={`flex ${displayDensity === 'compact' ? 'space-x-0.5' : 'space-x-1'}`}>
                              {project.ispublic && (
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 ${displayDensity === 'compact' ? 'px-1' : 'px-2'}`}>
                                  {displayDensity === 'compact' ? 'P' : 'Public'}
                                </span>
                              )}
                              {project.isfeatured && (
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800 ${displayDensity === 'compact' ? 'px-1' : 'px-2'}`}>
                                  {displayDensity === 'compact' ? 'F' : 'Featured'}
                                </span>
                              )}
                            </div>
                          )}
                          {column.key === 'manager' && (
                            <div className={`${getTextSize()} text-gray-900`}>
                              {project.teammembers?.name || 'Unassigned'}
                            </div>
                          )}
                          {column.key === 'updatedat' && project.updatedat && (
                            <div className={`${getTextSize()} text-gray-900`}>{formatDate(project.updatedat)}</div>
                          )}
                        </td>
                      ))}

                    <td className={`${getDensityClasses()} whitespace-nowrap text-right text-sm font-medium`}>
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => window.open(project.projecturl, '_blank')}
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                          title="Preview"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setEditingProject(project)
                            setIsEditModalOpen(true)
                          }}
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleToggleStatus(project.id.toString(), !project.ispublic)}
                          className={`text-gray-400 hover:text-green-600 transition-colors ${
                            project.ispublic ? 'text-green-600' : ''
                          }`}
                          title={project.ispublic ? 'Make Private' : 'Make Public'}
                        >
                          <PowerIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
                              handleDelete(project.id.toString())
                            }
                          }}
                          className="text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
      ) : viewMode === 'grid' ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {projects.length === 0 ? (
              <div className="col-span-full text-center py-12 text-gray-500">
                {debouncedSearchTerm ? 'No projects found matching your search.' : 'No projects found.'}
              </div>
            ) : (
              projects.map((project) => (
                <div
                  key={project.id}
                  className={`bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow ${
                    selectedProjects.includes(project.id.toString()) ? 'ring-2 ring-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <input
                      type="checkbox"
                      checked={selectedProjects.includes(project.id.toString())}
                      onChange={() => handleSelectProject(project.id.toString())}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div className="flex space-x-1">
                      {project.ispublic && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          Public
                        </span>
                      )}
                      {project.isfeatured && (
                        <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Featured
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="mb-3">
                    {project.imageurl ? (
                      <img
                        className="w-full h-32 object-cover rounded-lg"
                        src={project.imageurl}
                        alt={project.name}
                      />
                    ) : (
                      <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                        <span className="text-gray-500 text-2xl font-medium">
                          {project.name.charAt(0)}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="mb-3">
                    <h3 className="text-sm font-medium text-gray-900 mb-1">{project.name}</h3>
                    <p className="text-xs text-gray-500 line-clamp-2">{project.description}</p>
                  </div>

                  <div className="mb-3 space-y-1">
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">Client:</span>
                      <span className="text-gray-900">{project.clients?.companyname || 'No client'}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">Budget:</span>
                      <span className="text-gray-900">{project.estimatecost ? formatCurrency(project.estimatecost) : 'N/A'}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-500">Status:</span>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        project.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                        project.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                        project.status === 'PLANNING' ? 'bg-yellow-100 text-yellow-800' :
                        project.status === 'ON_HOLD' ? 'bg-gray-100 text-gray-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {project.status || 'Unknown'}
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => window.open(project.projecturl, '_blank')}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="Preview"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          setEditingProject(project)
                          setIsEditModalOpen(true)
                        }}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                        title="Edit"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleToggleStatus(project.id.toString(), !project.ispublic)}
                        className={`text-gray-400 hover:text-green-600 transition-colors ${
                          project.ispublic ? 'text-green-600' : ''
                        }`}
                        title={project.ispublic ? 'Make Private' : 'Make Public'}
                      >
                        <PowerIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
                            handleDelete(project.id.toString())
                          }
                        }}
                        className="text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      ) : (
        // Card view
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="space-y-4">
            {projects.length === 0 ? (
              <div className="text-center py-12 text-gray-500">
                {debouncedSearchTerm ? 'No projects found matching your search.' : 'No projects found.'}
              </div>
            ) : (
              projects.map((project) => (
                <div
                  key={project.id}
                  className={`bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow ${
                    selectedProjects.includes(project.id.toString()) ? 'ring-2 ring-blue-500' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    <input
                      type="checkbox"
                      checked={selectedProjects.includes(project.id.toString())}
                      onChange={() => handleSelectProject(project.id.toString())}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
                    />

                    <div className="flex-shrink-0">
                      {project.imageurl ? (
                        <img
                          className="h-16 w-16 rounded-lg object-cover"
                          src={project.imageurl}
                          alt={project.name}
                        />
                      ) : (
                        <div className="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-500 text-xl font-medium">
                            {project.name.charAt(0)}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 mb-1">{project.name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{project.description}</p>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-gray-500">Client:</span>
                              <div className="font-medium">{project.clients?.companyname || 'No client'}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Budget:</span>
                              <div className="font-medium">{project.estimatecost ? formatCurrency(project.estimatecost) : 'N/A'}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Start Date:</span>
                              <div className="font-medium">{project.projstartdate ? formatDate(project.projstartdate) : 'TBD'}</div>
                            </div>
                            <div>
                              <span className="text-gray-500">Manager:</span>
                              <div className="font-medium">{project.teammembers?.name || 'Unassigned'}</div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-3 ml-4">
                          <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                            project.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            project.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                            project.status === 'PLANNING' ? 'bg-yellow-100 text-yellow-800' :
                            project.status === 'ON_HOLD' ? 'bg-gray-100 text-gray-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {project.status || 'Unknown'}
                          </span>

                          <div className="flex space-x-1">
                            {project.ispublic && (
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Public
                              </span>
                            )}
                            {project.isfeatured && (
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Featured
                              </span>
                            )}
                          </div>

                          <div className="flex space-x-2">
                            <button
                              onClick={() => window.open(project.projecturl, '_blank')}
                              className="text-gray-400 hover:text-blue-600 transition-colors"
                              title="Preview"
                            >
                              <EyeIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => {
                                setEditingProject(project)
                                setIsEditModalOpen(true)
                              }}
                              className="text-gray-400 hover:text-blue-600 transition-colors"
                              title="Edit"
                            >
                              <PencilIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => handleToggleStatus(project.id.toString(), !project.ispublic)}
                              className={`text-gray-400 hover:text-green-600 transition-colors ${
                                project.ispublic ? 'text-green-600' : ''
                              }`}
                              title={project.ispublic ? 'Make Private' : 'Make Public'}
                            >
                              <PowerIcon className="h-5 w-5" />
                            </button>
                            <button
                              onClick={() => {
                                if (window.confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
                                  handleDelete(project.id.toString())
                                }
                              }}
                              className="text-gray-400 hover:text-red-600 transition-colors"
                              title="Delete"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Modals */}
      <ProjectModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create New Project"
      />

      <ProjectModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingProject(null)
        }}
        onSubmit={(formData) => editingProject && handleUpdate(editingProject.id, formData)}
        title="Edit Project"
        project={editingProject}
      />
    </div>
  )
}
