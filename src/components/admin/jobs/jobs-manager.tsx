'use client'

import React, { useState, useEffect, useRef } from 'react'
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon,
  RectangleStackIcon,
  AdjustmentsHorizontalIcon,
  EyeIcon,
  EyeSlashIcon,
  ChevronDownIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  BriefcaseIcon
} from '@heroicons/react/24/outline'
import { JobModal } from './job-modal'
import { CrudConfig } from '../crud/types'
import { motion, AnimatePresence } from 'framer-motion'

interface Job {
  id: string
  title: string
  description: string
  requirements: string
  location: string
  employmenttype: string
  salarymin?: number
  salarymax?: number
  salarycurrency: string
  isremote: boolean
  isactive: boolean
  expiresat?: string
  createdat: string
  updatedat?: string
  jobapplications?: Array<{
    id: string
    applicantname: string
    applicantemail: string
    status: string
    createdat: string
  }>
  [key: string]: any
}

interface JobsManagerProps {
  config: CrudConfig<Job>
}

export function JobsManager({ config }: JobsManagerProps) {
  const [jobs, setJobs] = useState<Job[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedJobs, setSelectedJobs] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingJob, setEditingJob] = useState<Job | null>(null)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const searchInputRef = useRef<HTMLInputElement>(null)

  // View controls state
  const [viewMode, setViewMode] = useState('list')
  const [displayDensity, setDisplayDensity] = useState('comfortable')
  const [visibleColumns, setVisibleColumns] = useState([
    'title', 'location', 'employmenttype', 'salary', 'isremote', 'isactive', 'applications', 'updatedat'
  ])
  const [sortBy, setSortBy] = useState('updatedat')
  const [sortOrder, setSortOrder] = useState('desc')
  const [showFilters, setShowFilters] = useState(false)
  const [showColumnMenu, setShowColumnMenu] = useState(false)
  const [showDensityMenu, setShowDensityMenu] = useState(false)
  const [showSortMenu, setShowSortMenu] = useState(false)
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchQuery)
    }, 300)

    return () => clearTimeout(timer)
  }, [searchQuery])

  // Reset to first page when search or filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [debouncedSearchTerm])

  // Fetch jobs
  const fetchJobs = async (preserveFocus = false) => {
    try {
      if (!preserveFocus) {
        setLoading(true)
      }

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
        sortBy: sortBy,
        sortOrder: sortOrder,
        ...(debouncedSearchTerm && { search: debouncedSearchTerm }),
      })

      const response = await fetch(`/api/admin/jobs?${params}`)
      if (!response.ok) throw new Error('Failed to fetch jobs')

      const data = await response.json()
      console.log('Jobs API Response:', data) // Debug log
      setJobs(data.data || [])
      setTotalPages(Math.ceil((data.total || 0) / 10))
      setError(null)
    } catch (err) {
      console.error('Error fetching jobs:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch jobs')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    const isSearching = debouncedSearchTerm !== ''
    fetchJobs(isSearching)
  }, [currentPage, debouncedSearchTerm, sortBy, sortOrder])

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      const response = await fetch('/api/admin/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) throw new Error('Failed to create job')

      setIsCreateModalOpen(false)
      fetchJobs()
      alert('Job created successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create job'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      const response = await fetch(`/api/admin/jobs/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) throw new Error('Failed to update job')

      setIsEditModalOpen(false)
      setEditingJob(null)
      fetchJobs()
      alert('Job updated successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update job'
      setError(errorMessage)
      alert(errorMessage)
      throw err
    }
  }

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const response = await fetch(`/api/admin/jobs/${id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      })

      if (!response.ok) throw new Error('Failed to delete job')

      setError(null)
      fetchJobs()
      console.log('Job deleted successfully')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete job'
      setError(errorMessage)
      console.error('Delete error:', err)
    }
  }

  // Toggle job status
  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/jobs/${id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isactive: isActive }),
      })

      if (!response.ok) throw new Error('Failed to update job status')

      fetchJobs()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update job status')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString()
  }

  const formatSalary = (min?: number, max?: number, currency = 'USD') => {
    if (!min && !max) return 'Not specified'
    if (min && max) return `${currency} ${min.toLocaleString()} - ${max.toLocaleString()}`
    if (min) return `${currency} ${min.toLocaleString()}+`
    if (max) return `Up to ${currency} ${max.toLocaleString()}`
    return 'Not specified'
  }

  // Handle select all
  const handleSelectAll = () => {
    if (selectedJobs.length === jobs.length) {
      setSelectedJobs([])
    } else {
      setSelectedJobs(jobs.map(job => String(job.id)))
    }
  }

  // Handle select individual job
  const handleSelectJob = (jobId: string) => {
    setSelectedJobs(prev =>
      prev.includes(jobId)
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    )
  }

  // View control functions
  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const handleColumnToggle = (columnKey: string) => {
    const isVisible = visibleColumns.includes(columnKey)
    if (isVisible) {
      setVisibleColumns(prev => prev.filter(col => col !== columnKey))
    } else {
      setVisibleColumns(prev => [...prev, columnKey])
    }
  }

  const resetViewSettings = () => {
    setVisibleColumns(['title', 'location', 'employmenttype', 'salary', 'isremote', 'isactive', 'applications', 'updatedat'])
    setViewMode('list')
    setDisplayDensity('comfortable')
    setSortBy('updatedat')
    setSortOrder('desc')
  }

  const availableColumns = [
    { key: 'title', label: 'Job Title', hideable: false },
    { key: 'location', label: 'Location', hideable: true },
    { key: 'employmenttype', label: 'Type', hideable: true },
    { key: 'salary', label: 'Salary', hideable: true },
    { key: 'isremote', label: 'Remote', hideable: true },
    { key: 'applications', label: 'Applications', hideable: true },
    { key: 'expiresat', label: 'Expires', hideable: true },
    { key: 'updatedat', label: 'Last Updated', hideable: true },
    { key: 'isactive', label: 'Status', hideable: true },
  ]

  const viewModeIcons = {
    list: ListBulletIcon,
    grid: Squares2X2Icon,
    card: RectangleStackIcon,
  }

  const densityLabels = {
    compact: 'Compact',
    comfortable: 'Comfortable',
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 mx-4 sm:mx-6 lg:mx-8 xl:mx-12 2xl:mx-16">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{config.title}</h1>
          {config.description && (
            <p className="text-gray-600">{config.description}</p>
          )}
        </div>
        <button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>+</span>
          <span>Add Job</span>
        </button>
      </div>

      {/* Comprehensive Search and Controls Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-4">
        {/* Search and Primary Controls */}
        <div className="flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4 flex-1">
            {/* Search Bar */}
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search jobs by title, location, type..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              />
              {/* Search Loading Indicator */}
              {searchQuery !== debouncedSearchTerm && (
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
                </div>
              )}
              {/* Clear Search Button */}
              {searchQuery && (
                <button
                  onClick={() => {
                    setSearchQuery('')
                    searchInputRef.current?.focus()
                  }}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              )}
            </div>

            {/* Filters Button */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 border rounded-lg transition-colors flex items-center space-x-2 ${
                showFilters
                  ? 'bg-blue-50 border-blue-300 text-blue-700'
                  : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <FunnelIcon className="h-4 w-4" />
              <span>Filters</span>
            </button>
          </div>

          {/* View Controls */}
          <div className="flex items-center space-x-2 bg-gray-50 border border-gray-200 rounded-lg p-1">
            {/* View Mode Controls */}
            <div className="flex items-center bg-white rounded-md p-1">
              {Object.entries(viewModeIcons).map(([mode, Icon]) => (
                <button
                  key={mode}
                  onClick={() => setViewMode(mode as 'list' | 'grid' | 'card')}
                  className={`p-2 rounded transition-colors ${
                    viewMode === mode
                      ? 'bg-blue-600 text-white shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`}
                  title={`${mode.charAt(0).toUpperCase() + mode.slice(1)} view`}
                >
                  <Icon className="h-4 w-4" />
                </button>
              ))}
            </div>

            {/* Display Density Controls */}
            <div className="relative">
              <button
                onClick={() => setShowDensityMenu(!showDensityMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Display density"
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                <span>{densityLabels[displayDensity]}</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showDensityMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[140px]"
                  >
                    {Object.entries(densityLabels).map(([density, label]) => (
                      <button
                        key={density}
                        onClick={() => {
                          setDisplayDensity(density as 'compact' | 'comfortable')
                          setShowDensityMenu(false)
                        }}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg transition-colors ${
                          displayDensity === density
                            ? 'bg-blue-50 text-blue-700'
                            : 'text-gray-700'
                        }`}
                      >
                        {label}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Column Visibility Controls */}
            <div className="relative">
              <button
                onClick={() => setShowColumnMenu(!showColumnMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Column visibility"
              >
                <EyeIcon className="h-4 w-4" />
                <span>Columns ({visibleColumns.length}/{availableColumns.filter(col => col.hideable !== false).length})</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showColumnMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[200px] max-h-64 overflow-y-auto"
                  >
                    <div className="p-2">
                      <div className="flex items-center justify-between mb-2 pb-2 border-b border-gray-100">
                        <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                          Column Visibility
                        </span>
                        <button
                          onClick={() => {
                            resetViewSettings()
                            setShowColumnMenu(false)
                          }}
                          className="text-xs text-blue-600 hover:text-blue-800"
                        >
                          Reset
                        </button>
                      </div>

                      {availableColumns
                        .filter(col => col.hideable !== false)
                        .map((column) => {
                          const isVisible = visibleColumns.includes(column.key)
                          return (
                            <label
                              key={column.key}
                              className="flex items-center space-x-2 py-1 hover:bg-gray-50 rounded cursor-pointer"
                            >
                              <input
                                type="checkbox"
                                checked={isVisible}
                                onChange={() => handleColumnToggle(column.key)}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="text-sm text-gray-700">{column.label}</span>
                              {isVisible ? (
                                <EyeIcon className="h-3 w-3 text-gray-400" />
                              ) : (
                                <EyeSlashIcon className="h-3 w-3 text-gray-400" />
                              )}
                            </label>
                          )
                        })}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Sort Controls */}
            <div className="relative">
              <button
                onClick={() => setShowSortMenu(!showSortMenu)}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-white rounded-md transition-colors"
                title="Sort options"
              >
                {sortOrder === 'asc' ? (
                  <ArrowUpIcon className="h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4" />
                )}
                <span>Sort</span>
                <ChevronDownIcon className="h-3 w-3" />
              </button>

              <AnimatePresence>
                {showSortMenu && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute top-full right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]"
                  >
                    <div className="p-2">
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2 pb-2 border-b border-gray-100">
                        Sort By
                      </div>
                      {availableColumns.map((column) => (
                        <button
                          key={column.key}
                          onClick={() => {
                            handleSort(column.key)
                            setShowSortMenu(false)
                          }}
                          className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-50 rounded transition-colors flex items-center justify-between ${
                            sortBy === column.key
                              ? 'bg-blue-50 text-blue-700'
                              : 'text-gray-700'
                          }`}
                        >
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </button>
                      ))}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Active Filters Display */}
        {(searchQuery || showFilters) && (
          <div className="flex items-center space-x-2 pt-2 border-t border-gray-100">
            <span className="text-sm text-gray-500">Active:</span>
            {searchQuery && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{searchQuery}"
                <button
                  onClick={() => setSearchQuery('')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <XMarkIcon className="h-3 w-3" />
                </button>
              </span>
            )}
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Sort: {availableColumns.find(col => col.key === sortBy)?.label} ({sortOrder === 'asc' ? 'A-Z' : 'Z-A'})
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              View: {viewMode.charAt(0).toUpperCase() + viewMode.slice(1)}
            </span>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
              Density: {densityLabels[displayDensity]}
            </span>
          </div>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* List View */}
        {viewMode === 'list' ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="w-12 px-6 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedJobs.length === jobs.length && jobs.length > 0}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>

                  {availableColumns
                    .filter(column => visibleColumns.includes(column.key))
                    .map((column) => (
                      <th
                        key={column.key}
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSort(column.key)}
                      >
                        <div className="flex items-center space-x-1">
                          <span>{column.label}</span>
                          {sortBy === column.key && (
                            sortOrder === 'asc' ? (
                              <ArrowUpIcon className="h-3 w-3" />
                            ) : (
                              <ArrowDownIcon className="h-3 w-3" />
                            )
                          )}
                        </div>
                      </th>
                    ))}

                  <th className="w-32 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {jobs.map((job, index) => (
                  <motion.tr
                    key={job.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className={`hover:bg-gray-50 transition-colors ${
                      selectedJobs.includes(job.id.toString()) ? 'bg-blue-50' : ''
                    } ${displayDensity === 'compact' ? 'h-12' : 'h-16'}`}
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedJobs.includes(job.id.toString())}
                        onChange={() => handleSelectJob(job.id.toString())}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>

                    {availableColumns
                      .filter(column => visibleColumns.includes(column.key))
                      .map((column) => (
                        <td key={column.key} className="px-6 py-4 whitespace-nowrap">
                          {column.key === 'title' && (
                            <div className="text-sm font-medium text-gray-900">{job.title}</div>
                          )}
                          {column.key === 'location' && (
                            <div className="text-sm text-gray-900">{job.location}</div>
                          )}
                          {column.key === 'employmenttype' && (
                            <div className="text-sm text-gray-900">{job.employmenttype}</div>
                          )}
                          {column.key === 'salary' && (
                            <div className="text-sm text-gray-900">
                              {formatSalary(job.salarymin, job.salarymax, job.salarycurrency)}
                            </div>
                          )}
                          {column.key === 'isremote' && (
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              job.isremote
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {job.isremote ? 'Remote' : 'On-site'}
                            </span>
                          )}
                          {column.key === 'isactive' && (
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              job.isactive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {job.isactive ? 'Active' : 'Inactive'}
                            </span>
                          )}
                          {column.key === 'applications' && (
                            <div className="text-sm text-gray-900">
                              {job.jobapplications?.length || 0}
                            </div>
                          )}
                          {column.key === 'expiresat' && job.expiresat && (
                            <div className="text-sm text-gray-900">{formatDate(job.expiresat)}</div>
                          )}
                          {column.key === 'createdat' && (
                            <div className="text-sm text-gray-900">{formatDate(job.createdat)}</div>
                          )}
                          {column.key === 'updatedat' && job.updatedat && (
                            <div className="text-sm text-gray-900">{formatDate(job.updatedat)}</div>
                          )}
                        </td>
                      ))}

                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => window.open(`/jobs/${job.id}`, '_blank')}
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                          title="Preview"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            setEditingJob(job)
                            setIsEditModalOpen(true)
                          }}
                          className="text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleToggleStatus(job.id.toString(), !job.isactive)}
                          className={`text-gray-400 hover:text-green-600 transition-colors ${
                            job.isactive ? 'text-green-600' : ''
                          }`}
                          title={job.isactive ? 'Deactivate' : 'Activate'}
                        >
                          <PowerIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => {
                            if (window.confirm('Are you sure you want to delete this job? This action cannot be undone.')) {
                              handleDelete(job.id.toString())
                            }
                          }}
                          className="text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="p-6 text-center text-gray-500">
            Other view modes coming soon...
          </div>
        )}
      </div>

      {/* Create Modal */}
      <JobModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create Job"
      />

      {/* Edit Modal */}
      <JobModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false)
          setEditingJob(null)
        }}
        onSubmit={(data) => editingJob && handleUpdate(editingJob.id.toString(), data)}
        title="Edit Job"
        initialData={editingJob}
      />
    </div>
  )
}
