import { PrismaClient } from '@prisma/client'
import { Faker, en } from '@faker-js/faker'

const prisma = new PrismaClient()

// Create a new Faker instance with the English locale
const faker = new Faker({ locale: [en] })

// Utility functions
const generateSlug = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '')
}

const randomChoice = <T>(array: T[]): T => {
  return array[Math.floor(Math.random() * array.length)]
}

const randomChoices = <T>(array: T[], count: number): T[] => {
  const shuffled = [...array].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, Math.min(count, array.length))
}

// Seed data constants - English only
const CATEGORIES = [
  { name: 'Web Development', desc: 'Custom web applications and responsive websites' },
  { name: 'Mobile Development', desc: 'Native iOS and Android mobile applications' },
  { name: 'UI/UX Design', desc: 'User interface and user experience design services' },
  { name: 'Digital Marketing', desc: 'SEO, SEM, social media marketing and analytics' },
  { name: 'Cloud Solutions', desc: 'Cloud infrastructure and deployment services' },
  { name: 'E-commerce', desc: 'Online store development and payment integration' },
  { name: 'Consulting', desc: 'Technology strategy and business consulting' },
]

const TECHNOLOGIES = [
  { name: 'React', desc: 'Modern JavaScript library for building user interfaces' },
  { name: 'Next.js', desc: 'Full-stack React framework for production applications' },
  { name: 'TypeScript', desc: 'Strongly typed programming language built on JavaScript' },
  { name: 'Node.js', desc: 'JavaScript runtime for server-side development' },
  { name: 'PostgreSQL', desc: 'Advanced open source relational database system' },
  { name: 'Prisma', desc: 'Next-generation ORM for Node.js and TypeScript' },
  { name: 'Tailwind CSS', desc: 'Utility-first CSS framework for rapid UI development' },
  { name: 'Python', desc: 'High-level programming language for web and data science' },
  { name: 'Django', desc: 'High-level Python web framework for rapid development' },
  { name: 'AWS', desc: 'Amazon Web Services cloud computing platform' },
  { name: 'Docker', desc: 'Containerization platform for application deployment' },
  { name: 'MongoDB', desc: 'NoSQL document database for modern applications' },
]

// English company names and locations
const COMPANY_NAMES = [
  'TechCorp Solutions', 'Digital Innovations Inc', 'WebCraft Studios', 'DataFlow Systems',
  'CloudTech Enterprises', 'InnovateLab', 'SmartSolutions LLC', 'NextGen Technologies',
  'PixelPerfect Design', 'CodeCraft Development', 'StreamlineIT', 'FutureTech Partners',
  'AgileWorks', 'BrightIdeas Co', 'TechVision Group', 'DigitalEdge Solutions'
]

const CITIES = [
  'New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia',
  'San Antonio', 'San Diego', 'Dallas', 'San Jose', 'Austin', 'Jacksonville',
  'Fort Worth', 'Columbus', 'Charlotte', 'San Francisco', 'Indianapolis',
  'Seattle', 'Denver', 'Washington', 'Boston', 'Nashville', 'Baltimore',
  'Oklahoma City', 'Louisville', 'Portland', 'Las Vegas', 'Milwaukee', 'Albuquerque'
]

const STATES = [
  'California', 'Texas', 'Florida', 'New York', 'Pennsylvania', 'Illinois',
  'Ohio', 'Georgia', 'North Carolina', 'Michigan', 'New Jersey', 'Virginia',
  'Washington', 'Arizona', 'Massachusetts', 'Tennessee', 'Indiana', 'Missouri',
  'Maryland', 'Wisconsin', 'Colorado', 'Minnesota', 'South Carolina', 'Alabama'
]

const JOB_TITLES = [
  'Software Engineer', 'Senior Developer', 'Project Manager', 'UI/UX Designer',
  'Data Analyst', 'DevOps Engineer', 'Product Manager', 'Quality Assurance Engineer',
  'Business Analyst', 'Technical Lead', 'Frontend Developer', 'Backend Developer',
  'Full Stack Developer', 'Mobile Developer', 'Cloud Architect', 'Scrum Master'
]

const SERVICE_NAMES = [
  'Custom Web Application', 'E-commerce Platform', 'Mobile App Development',
  'UI/UX Design Package', 'SEO Optimization', 'Cloud Migration Service',
  'Database Design', 'API Development', 'Website Redesign', 'Digital Marketing Campaign',
  'Technical Consulting', 'Performance Optimization', 'Security Audit', 'Maintenance Package'
]

const PROJECT_NAMES = [
  'E-commerce Platform Redesign', 'Mobile Banking App', 'Customer Portal Development',
  'Inventory Management System', 'Real Estate Platform', 'Healthcare Dashboard',
  'Learning Management System', 'Social Media Analytics Tool', 'CRM Integration',
  'Payment Gateway Implementation', 'Content Management System', 'Business Intelligence Dashboard'
]

const PROJECT_STATUSES = ['PLANNING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']
const INVOICE_STATUSES = ['DRAFT', 'SENT', 'PAID', 'OVERDUE']
const ORDER_STATUSES = ['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED']
const CONTRACT_STATUSES = ['DRAFT', 'PENDING', 'SIGNED', 'ACTIVE', 'COMPLETED']
const EMPLOYMENT_TYPES = ['FULL_TIME', 'PART_TIME', 'CONTRACT', 'FREELANCE']
const PAYMENT_METHODS = ['CREDIT_CARD', 'BANK_TRANSFER', 'PAYPAL', 'CHECK']

async function main() {
  console.log('🌱 Starting database seeding...')

  // Clear existing data in dependency order
  await prisma.payments.deleteMany()
  await prisma.invoiceitems.deleteMany()
  await prisma.invoices.deleteMany()
  await prisma.contracts.deleteMany()
  await prisma.orderdetails.deleteMany()
  await prisma.serviceoptionfeatures.deleteMany()
  await prisma.serviceoptions.deleteMany()
  await prisma.services.deleteMany()
  await prisma.categories.deleteMany()
  await prisma.tasks.deleteMany()
  await prisma.payrollrecords.deleteMany()
  await prisma.projectdocuments.deleteMany()
  await prisma.messages.deleteMany()
  await prisma.projecttechnologies.deleteMany()
  await prisma.projects.deleteMany()
  await prisma.orders.deleteMany()
  await prisma.testimonials.deleteMany()
  await prisma.feedbacks.deleteMany()
  await prisma.clients.deleteMany()
  await prisma.teammembers.deleteMany()
  await prisma.technologies.deleteMany()
  await prisma.jobapplications.deleteMany()
  await prisma.joblistings.deleteMany()
  await prisma.contactforms.deleteMany()
  await prisma.blogposts.deleteMany()
  await prisma.legalpagesections.deleteMany()
  await prisma.legalpages.deleteMany()
  await prisma.sitesettings.deleteMany()
  await prisma.chatbotquickactions.deleteMany()
  await prisma.chatbotresponses.deleteMany()
  await prisma.chatbotkeywords.deleteMany()
  await prisma.chatbotintents.deleteMany()
  await prisma.datauploadlogs.deleteMany()
  await prisma.users.deleteMany()

  console.log('🗑️  Cleared existing data')

  // 1. Create Users (no dependencies)
  console.log('👥 Creating users...')
  const users = await Promise.all([
    // Create main admin user
    prisma.users.create({
      data: {
        email: '<EMAIL>',
        firstname: 'Admin',
        lastname: 'User',
        role: 'ADMIN',
        password: '$2a$12$AWNX67hEKN3vLKWOA5qH2uqhG9WQGEPVKj3aGejTozEYq3KN5i01O', // password123
        imageurl: faker.image.avatar(),
        emailverified: new Date(),
      },
    }),
    // Create additional admin users
    ...Array.from({ length: 2 }, () =>
      prisma.users.create({
        data: {
          email: faker.internet.email(),
          firstname: faker.person.firstName(),
          lastname: faker.person.lastName(),
          role: 'ADMIN',
          password: '$2a$12$AWNX67hEKN3vLKWOA5qH2uqhG9WQGEPVKj3aGejTozEYq3KN5i01O', // password123
          imageurl: faker.image.avatar(),
          emailverified: faker.date.past(),
        },
      })
    ),
    // Create regular users
    ...Array.from({ length: 4 }, () =>
      prisma.users.create({
        data: {
          email: faker.internet.email(),
          firstname: faker.person.firstName(),
          lastname: faker.person.lastName(),
          role: 'USER',
          password: '$2a$12$AWNX67hEKN3vLKWOA5qH2uqhG9WQGEPVKj3aGejTozEYq3KN5i01O', // password123
          imageurl: faker.image.avatar(),
          emailverified: faker.date.past(),
        },
      })
    ),
    // Create client users for chat system
    ...Array.from({ length: 6 }, () =>
      prisma.users.create({
        data: {
          email: faker.internet.email(),
          firstname: faker.person.firstName(),
          lastname: faker.person.lastName(),
          role: 'CLIENT',
          password: '$2a$12$AWNX67hEKN3vLKWOA5qH2uqhG9WQGEPVKj3aGejTozEYq3KN5i01O', // password123
          imageurl: faker.image.avatar(),
          emailverified: faker.date.past(),
        },
      })
    ),
  ])

  // 2. Create Categories (no dependencies)
  console.log('📂 Creating categories...')
  const categories = await Promise.all(
    CATEGORIES.map((cat, index) =>
      prisma.categories.create({
        data: {
          categname: cat.name,
          categdesc: cat.desc,
          displayorder: index,
          isactive: true,
        },
      })
    )
  )

  // 3. Create Technologies (no dependencies)
  console.log('⚡ Creating technologies...')
  const technologies = await Promise.all(
    TECHNOLOGIES.map((tech, index) =>
      prisma.technologies.create({
        data: {
          name: tech.name,
          description: tech.desc,
          iconurl: faker.image.url({ width: 64, height: 64 }),
          displayorder: index,
          isactive: true,
        },
      })
    )
  )

  // 4. Create Team Members (no dependencies)
  console.log('👨‍💼 Creating team members...')
  const teamMembers = await Promise.all(
    Array.from({ length: 8 }, (_, index) =>
      prisma.teammembers.create({
        data: {
          name: `${faker.person.firstName()} ${faker.person.lastName()}`,
          position: randomChoice(JOB_TITLES),
          birthdate: faker.date.birthdate({ min: 25, max: 55, mode: 'age' }),
          gender: randomChoice(['Male', 'Female', 'Other']),
          maritalstatus: randomChoice(['Single', 'Married', 'Divorced']),
          hiredate: faker.date.past({ years: 5 }),
          address: `${faker.number.int({ min: 100, max: 9999 })} ${faker.location.street()}`,
          city: randomChoice(CITIES),
          state: randomChoice(STATES),
          zipcode: faker.location.zipCode(),
          country: 'United States',
          phone: faker.phone.number(),
          email: faker.internet.email(),
          salary: faker.number.float({ min: 50000, max: 150000, fractionDigits: 2 }),
          payrollmethod: randomChoice(['DIRECT_DEPOSIT', 'CHECK', 'WIRE']),
          bio: `Experienced ${randomChoice(JOB_TITLES).toLowerCase()} with expertise in modern web technologies and agile development methodologies.`,
          photourl: faker.image.avatar(),
          linkedinurl: `https://linkedin.com/in/${faker.internet.displayName().toLowerCase().replace(/\s+/g, '')}`,
          githuburl: `https://github.com/${faker.internet.displayName().toLowerCase().replace(/\s+/g, '')}`,
          displayorder: index,
          isactive: true,
        },
      })
    )
  )

  // 5. Create Clients (depends on users)
  console.log('🏢 Creating clients...')
  const clients = await Promise.all(
    Array.from({ length: 15 }, () =>
      prisma.clients.create({
        data: {
          userid: randomChoice([null, ...users.map(u => u.id)]),
          companyname: randomChoice(COMPANY_NAMES),
          contactname: `${faker.person.firstName()} ${faker.person.lastName()}`,
          contactposition: randomChoice(JOB_TITLES),
          contactemail: faker.internet.email(),
          contactphone: faker.phone.number(),
          companywebsite: `https://www.${faker.internet.domainName()}`,
          address: `${faker.number.int({ min: 100, max: 9999 })} ${faker.location.street()}`,
          city: randomChoice(CITIES),
          state: randomChoice(STATES),
          zipcode: faker.location.zipCode(),
          country: 'United States',
          logourl: faker.image.url({ width: 200, height: 200 }),
          notes: `Professional client with focus on ${randomChoice(['technology solutions', 'digital transformation', 'business growth', 'innovation', 'market expansion'])}.`,
          isactive: true,
        },
      })
    )
  )

  // 6. Create Services (depends on categories)
  console.log('🛠️ Creating services...')
  const services = await Promise.all(
    categories.flatMap((category, categoryIndex) =>
      Array.from({ length: 3 }, (_, serviceIndex) =>
        prisma.services.create({
          data: {
            categid: category.id,
            name: randomChoice(SERVICE_NAMES),
            description: `Professional ${category.categname.toLowerCase()} service with comprehensive planning, development, and support.`,
            iconclass: `fas fa-${randomChoice(['code', 'mobile', 'paint-brush', 'chart-line', 'cogs', 'laptop', 'database', 'cloud'])}`,
            price: faker.number.float({ min: 500, max: 50000, fractionDigits: 2 }),
            discountrate: faker.number.int({ min: 0, max: 20 }),
            manager: `${faker.person.firstName()} ${faker.person.lastName()}`,
            isactive: true,
            displayorder: categoryIndex * 3 + serviceIndex,
          },
        })
      )
    )
  )

  // 7. Create Service Options (depends on services)
  console.log('⚙️ Creating service options...')
  const serviceOptions = await Promise.all(
    services.flatMap(service =>
      Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
        prisma.serviceoptions.create({
          data: {
            servid: service.id,
            optname: faker.commerce.productAdjective(),
            optprice: faker.number.float({ min: 100, max: 5000, fractionDigits: 2 }),
            optdiscountrate: faker.number.int({ min: 0, max: 15 }),
            optdesc: faker.lorem.sentence(),
            isactive: true,
          },
        })
      )
    )
  )

  // 8. Create Service Option Features (depends on service options)
  console.log('✨ Creating service option features...')
  await Promise.all(
    serviceOptions.flatMap(option =>
      Array.from({ length: faker.number.int({ min: 1, max: 4 }) }, () =>
        prisma.serviceoptionfeatures.create({
          data: {
            optid: option.id,
            featname: faker.commerce.productMaterial(),
            featcost: faker.number.float({ min: 50, max: 1000, fractionDigits: 2 }),
            featdiscountrate: faker.number.int({ min: 0, max: 10 }),
            featdesc: faker.lorem.sentence(),
            isincluded: faker.datatype.boolean(),
          },
        })
      )
    )
  )

  // 9. Create Orders (depends on clients and team members)
  console.log('📋 Creating orders...')
  const orders = await Promise.all(
    Array.from({ length: 20 }, (_, index) =>
      prisma.orders.create({
        data: {
          ordertitle: `${randomChoice(SERVICE_NAMES)} - Order #${String(index + 1).padStart(3, '0')}`,
          clientid: randomChoice(clients).id,
          ordermanager: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          orderdesc: `Professional service order for ${randomChoice(['web development', 'mobile application', 'digital marketing', 'consulting services', 'cloud solutions'])} with comprehensive project management and support.`,
          orderdate: faker.date.past(),
          ordertotalamount: faker.number.float({ min: 1000, max: 100000, fractionDigits: 2 }),
          orderdiscountrate: faker.number.int({ min: 0, max: 20 }),
          status: randomChoice(ORDER_STATUSES),
          notes: `Order processed and assigned to project team. Expected delivery within agreed timeline.`,
          isactive: true,
        },
      })
    )
  )

  // 10. Create Order Details (depends on orders, services, options, features)
  console.log('📝 Creating order details...')
  await Promise.all(
    orders.flatMap(order =>
      Array.from({ length: faker.number.int({ min: 1, max: 3 }) }, () =>
        prisma.orderdetails.create({
          data: {
            orderid: order.id,
            servid: randomChoice(services).id,
            optid: randomChoice([null, ...serviceOptions.map(so => so.id)]),
            costeach: faker.number.float({ min: 100, max: 10000, fractionDigits: 2 }),
            discountrate: faker.number.int({ min: 0, max: 15 }),
            comments: faker.lorem.sentence(),
            notes: faker.lorem.sentence(),
            isactive: true,
          },
        })
      )
    )
  )

  // 11. Create Projects (depends on orders, clients, team members)
  console.log('🚀 Creating projects...')
  const projects = await Promise.all(
    Array.from({ length: 12 }, (_, index) =>
      prisma.projects.create({
        data: {
          name: randomChoice(PROJECT_NAMES),
          description: `Comprehensive ${randomChoice(['web application', 'mobile solution', 'digital platform', 'software system'])} designed to enhance business operations and user experience.`,
          projgoals: `Deliver a high-quality, scalable solution that meets client requirements and exceeds expectations.`,
          projmanager: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          clientid: randomChoice([null, ...clients.map(c => c.id)]),
          orderid: randomChoice(orders).id,
          imageurl: faker.image.url({ width: 800, height: 600 }),
          projecturl: `https://project-${index + 1}.technoloway.com`,
          githuburl: `https://github.com/technoloway/project-${index + 1}`,
          tags: randomChoices(['React', 'Next.js', 'TypeScript', 'Node.js', 'PostgreSQL', 'AWS', 'Docker', 'Tailwind CSS'], 4).join(','),
          projstartdate: faker.date.past(),
          projcompletiondate: faker.date.future(),
          estimatecost: faker.number.float({ min: 5000, max: 100000, fractionDigits: 2 }),
          estimatetime: `${faker.number.int({ min: 1, max: 12 })} months`,
          estimateeffort: `${faker.number.int({ min: 100, max: 1000 })} hours`,
          status: randomChoice(PROJECT_STATUSES),
          isfeatured: faker.datatype.boolean(),
          ispublic: faker.datatype.boolean(),
          displayorder: index,
        },
      })
    )
  )

  // 12. Create Project Technologies (depends on projects and technologies)
  console.log('🔧 Creating project technologies...')
  await Promise.all(
    projects.flatMap(project =>
      randomChoices(technologies, faker.number.int({ min: 2, max: 5 })).map(tech =>
        prisma.projecttechnologies.create({
          data: {
            projectsid: project.id,
            technologiesid: tech.id,
          },
        })
      )
    )
  )

  // 13. Create Contracts (depends on projects, clients, orders, team members)
  console.log('📄 Creating contracts...')
  const contracts = await Promise.all(
    Array.from({ length: 8 }, () => {
      const project = randomChoice(projects)
      return prisma.contracts.create({
        data: {
          contname: faker.commerce.productName(),
          projid: project.id,
          clientid: project.clientid || randomChoice(clients).id,
          orderid: project.orderid,
          contmanager: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          contservtype: randomChoice(['Development', 'Consulting', 'Design', 'Support']),
          contlang: 'English',
          agreementdesc: faker.lorem.paragraph(),
          contvalue: faker.number.float({ min: 10000, max: 200000, fractionDigits: 2 }),
          contvaluecurr: 'USD',
          billingtype: randomChoice(['Fixed', 'Hourly', 'Monthly']),
          nextbilldate: faker.date.future(),
          contsignmethod: randomChoice(['Electronic', 'Physical', 'Digital']),
          contsigneddate: faker.date.past(),
          contexecuteddate: faker.date.past(),
          contexpirydate: faker.date.future(),
          contstatus: randomChoice(CONTRACT_STATUSES),
          lastupdateuser: randomChoice([null, ...teamMembers.map(tm => tm.id)]),
          comments: faker.lorem.sentence(),
          notes: faker.lorem.paragraph(),
        },
      })
    })
  )

  // 14. Create Invoices (depends on clients, contracts, orders, projects)
  console.log('💰 Creating invoices...')
  const invoices = await Promise.all(
    Array.from({ length: 15 }, () => {
      const contract = randomChoice(contracts)
      return prisma.invoices.create({
        data: {
          duedate: faker.date.future(),
          subtotal: faker.number.float({ min: 1000, max: 50000, fractionDigits: 2 }),
          taxrate: faker.number.float({ min: 0, max: 0.15, fractionDigits: 4 }),
          taxamount: faker.number.float({ min: 100, max: 5000, fractionDigits: 2 }),
          totalamount: faker.number.float({ min: 1100, max: 55000, fractionDigits: 2 }),
          status: randomChoice(INVOICE_STATUSES),
          description: faker.lorem.sentence(),
          clientid: contract.clientid,
          contid: contract.id,
          orderid: contract.orderid,
          projectid: randomChoice([null, contract.projid]),
          paidat: faker.datatype.boolean() ? faker.date.past() : null,
        },
      })
    })
  )

  // 15. Create Invoice Items (depends on invoices)
  console.log('📋 Creating invoice items...')
  await Promise.all(
    invoices.flatMap(invoice =>
      Array.from({ length: faker.number.int({ min: 1, max: 5 }) }, () =>
        prisma.invoiceitems.create({
          data: {
            description: faker.commerce.productName(),
            quantity: faker.number.float({ min: 1, max: 10, fractionDigits: 2 }),
            unitprice: faker.number.float({ min: 50, max: 5000, fractionDigits: 2 }),
            totalprice: faker.number.float({ min: 50, max: 50000, fractionDigits: 2 }),
            invoiceid: invoice.id,
          },
        })
      )
    )
  )

  // 16. Create Payments (depends on invoices)
  console.log('💳 Creating payments...')
  await Promise.all(
    invoices
      .filter(() => faker.datatype.boolean())
      .flatMap(invoice =>
        Array.from({ length: faker.number.int({ min: 1, max: 2 }) }, () =>
          prisma.payments.create({
            data: {
              amount: faker.number.float({ min: 100, max: Number(invoice.totalamount), fractionDigits: 2 }),
              paymentdate: faker.date.past(),
              paymentmethod: randomChoice(PAYMENT_METHODS),
              status: randomChoice(['Completed', 'Pending', 'Failed']),
              notes: faker.lorem.sentence(),
              invoiceid: invoice.id,
            },
          })
        )
      )
  )

  // 17. Create Testimonials (depends on clients)
  console.log('⭐ Creating testimonials...')
  const testimonialTexts = [
    "Technoloway delivered an exceptional web application that exceeded our expectations. Their team's expertise and attention to detail made the entire process smooth and professional.",
    "Working with Technoloway was a game-changer for our business. They transformed our outdated system into a modern, efficient platform that our customers love.",
    "The mobile app developed by Technoloway has significantly improved our customer engagement. Their innovative approach and technical skills are truly impressive.",
    "Technoloway's consulting services helped us streamline our operations and implement best practices. Their insights were invaluable to our digital transformation.",
    "Outstanding work on our e-commerce platform! Technoloway's team delivered on time and within budget, with excellent ongoing support.",
    "The UI/UX design created by Technoloway is both beautiful and functional. Our users have provided overwhelmingly positive feedback.",
    "Technoloway's cloud migration services were seamless and efficient. They handled everything professionally and kept us informed throughout the process.",
    "Excellent project management and communication throughout our development project. Technoloway truly understands client needs and delivers results.",
    "The custom software solution developed by Technoloway has revolutionized our workflow. Highly recommend their services to any business.",
    "Professional, reliable, and innovative - Technoloway exceeded our expectations in every aspect of our project collaboration."
  ]

  await Promise.all(
    Array.from({ length: 10 }, (_, index) => {
      const client = randomChoice(clients)
      return prisma.testimonials.create({
        data: {
          clientid: client.id,
          clientname: client.contactname,
          clienttitle: client.contactposition || randomChoice(JOB_TITLES),
          clientcompany: client.companyname,
          clientphotourl: faker.image.avatar(),
          content: testimonialTexts[index],
          rating: faker.number.int({ min: 4, max: 5 }),
          isfeatured: faker.datatype.boolean(),
          displayorder: index,
        },
      })
    })
  )

  // 18. Create Blog Posts (depends on users)
  console.log('📝 Creating blog posts...')
  const blogTitles = [
    'Getting Started with React and TypeScript',
    'Building Scalable Web Applications with Next.js',
    'Modern UI/UX Design Principles for 2024',
    'Cloud Migration Strategies for Small Businesses',
    'The Future of Mobile App Development',
    'Best Practices for Database Design',
    'Implementing DevOps in Your Development Workflow',
    'Digital Marketing Trends and Strategies'
  ]

  await Promise.all(
    Array.from({ length: 8 }, (_, index) =>
      prisma.blogposts.create({
        data: {
          authorid: randomChoice(users).id.toString(),
          title: blogTitles[index],
          content: `This comprehensive guide explores the latest trends and best practices in modern web development. We'll cover essential concepts, practical examples, and real-world applications that will help you build better software solutions.\n\nIn today's rapidly evolving technology landscape, staying up-to-date with the latest tools and methodologies is crucial for success. This article provides insights from industry experts and practical advice for developers at all levels.\n\nWhether you're just starting your development journey or looking to enhance your existing skills, this guide offers valuable information and actionable strategies that you can implement in your projects immediately.`,
          slug: generateSlug(blogTitles[index]),
          featuredimageurl: faker.image.url({ width: 800, height: 600 }),
          excerpt: `Learn about the latest trends and best practices in ${randomChoice(['web development', 'mobile applications', 'digital marketing', 'cloud computing', 'software engineering'])}.`,
          ispublished: faker.datatype.boolean(),
          publishedat: faker.datatype.boolean() ? faker.date.past() : null,
          categories: randomChoices(['Technology', 'Development', 'Design', 'Business', 'Tutorial'], 2).join(','),
          tags: randomChoices(['React', 'Next.js', 'TypeScript', 'Node.js', 'UI/UX', 'Mobile', 'Cloud', 'DevOps'], 3).join(','),
        },
      })
    )
  )

  // 19. Create Job Listings (no dependencies)
  console.log('💼 Creating job listings...')
  const jobListings = await Promise.all(
    Array.from({ length: 6 }, () =>
      prisma.joblistings.create({
        data: {
          title: faker.person.jobTitle(),
          description: faker.lorem.paragraphs(3),
          requirements: faker.lorem.paragraphs(2),
          location: faker.location.city(),
          employmenttype: randomChoice(EMPLOYMENT_TYPES),
          salarymin: faker.number.float({ min: 40000, max: 80000, fractionDigits: 2 }),
          salarymax: faker.number.float({ min: 80000, max: 150000, fractionDigits: 2 }),
          salarycurrency: 'USD',
          isremote: faker.datatype.boolean(),
          isactive: true,
          expiresat: faker.date.future(),
        },
      })
    )
  )

  // 20. Create Job Applications (depends on job listings)
  console.log('📄 Creating job applications...')
  await Promise.all(
    jobListings.flatMap(job =>
      Array.from({ length: faker.number.int({ min: 2, max: 8 }) }, () =>
        prisma.jobapplications.create({
          data: {
            applicantname: faker.person.fullName(),
            applicantemail: faker.internet.email(),
            applicantphone: faker.phone.number(),
            resumeurl: faker.internet.url(),
            coverletter: faker.lorem.paragraphs(2),
            status: randomChoice(['PENDING', 'REVIEWED', 'INTERVIEW', 'HIRED', 'REJECTED']),
            notes: faker.lorem.sentence(),
            joblistingid: job.id,
          },
        })
      )
    )
  )

  // 21. Create Contact Forms and Chat Messages (depends on users and clients)
  console.log('📞 Creating contact forms and chat messages...')

  // Create initial contact forms (traditional contact form submissions)
  const contactForms = await Promise.all(
    Array.from({ length: 8 }, () =>
      prisma.contactforms.create({
        data: {
          name: faker.person.fullName(),
          email: faker.internet.email(),
          phone: faker.phone.number(),
          subject: faker.lorem.sentence(),
          message: faker.lorem.paragraphs(2),
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          status: randomChoice(['New', 'In Progress', 'Resolved', 'Closed']),
          messagetype: 'contact',
          contenttype: 'text',
        },
      })
    )
  )

  // Create chat conversations between users
  console.log('💬 Creating chat conversations...')

  // Get admin and client users for chat conversations
  const adminUsers = users.filter(u => u.role === 'ADMIN')
  const clientUsers = users.filter(u => u.role === 'USER' || u.role === 'CLIENT')
  const allChatUsers = [...adminUsers, ...clientUsers]

  // Create chat threads (conversation starters)
  const chatThreads = await Promise.all(
    Array.from({ length: 6 }, (_, threadIndex) => {
      const sender = randomChoice(clientUsers)
      const receiver = randomChoice(adminUsers)
      const threadId = BigInt(Date.now() + threadIndex * 1000) // Unique thread ID

      return prisma.contactforms.create({
        data: {
          name: sender.firstname + ' ' + (sender.lastname || ''),
          email: sender.email,
          phone: faker.phone.number(),
          subject: randomChoice([
            'Project Inquiry',
            'Technical Support',
            'Service Question',
            'Billing Question',
            'General Discussion',
            'Feature Request'
          ]),
          message: randomChoice([
            'Hi! I have a question about your web development services. Could you help me understand the process?',
            'Hello, I need some technical support with my current project. Are you available to chat?',
            'Good morning! I\'m interested in your mobile app development services. Can we discuss the details?',
            'Hi there! I have some questions about the billing for my recent project. Could you clarify?',
            'Hello! I\'d like to discuss a potential new project with your team. When would be a good time?',
            'Hi! I have an idea for a new feature that might be useful. Would love to get your thoughts!'
          ]),
          senderid: sender.id,
          receiverid: receiver.id,
          threadid: threadId,
          messagetype: 'chat',
          contenttype: 'text',
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          status: randomChoice(['New', 'In Progress', 'Resolved']),
          isdelivered: true,
          deliveredat: faker.date.past(),
          createdat: faker.date.past({ days: 30 }),
        },
      })
    })
  )

  // Create replies and ongoing conversations for each thread
  console.log('💭 Creating chat replies and conversations...')

  const chatReplies = []
  for (const thread of chatThreads) {
    const numReplies = faker.number.int({ min: 2, max: 8 })
    const threadUsers = [
      { id: thread.senderid!, role: 'client' },
      { id: thread.receiverid!, role: 'admin' }
    ]

    for (let i = 0; i < numReplies; i++) {
      // Alternate between sender and receiver for realistic conversation
      const isAdminReply = i % 2 === 0 // First reply is usually from admin
      const currentSender = isAdminReply ? threadUsers[1] : threadUsers[0]
      const currentReceiver = isAdminReply ? threadUsers[0] : threadUsers[1]

      const adminReplies = [
        'Thanks for reaching out! I\'d be happy to help you with that.',
        'Great question! Let me provide you with some detailed information.',
        'I understand your concern. Here\'s what I can tell you about this.',
        'That sounds like an interesting project! Let\'s discuss the requirements.',
        'I\'ve reviewed your request and here are my recommendations.',
        'Perfect timing! We actually have some great solutions for this.',
        'I can definitely help you with that. Let me explain the process.',
        'Thanks for the feedback! That\'s a really valuable suggestion.'
      ]

      const clientReplies = [
        'That\'s exactly what I was looking for! Thank you for the explanation.',
        'This sounds perfect for our needs. What would be the next steps?',
        'Great! Could you also tell me about the timeline and pricing?',
        'I appreciate the quick response. This helps a lot!',
        'That makes sense. I have a few more questions if you don\'t mind.',
        'Excellent! I\'m excited to move forward with this project.',
        'Thank you for the detailed information. Very helpful!',
        'Perfect! I\'ll discuss this with my team and get back to you.'
      ]

      const replyMessage = isAdminReply
        ? randomChoice(adminReplies)
        : randomChoice(clientReplies)

      const reply = await prisma.contactforms.create({
        data: {
          name: isAdminReply ? 'Admin Support' : thread.name,
          email: isAdminReply ? '<EMAIL>' : thread.email,
          phone: thread.phone,
          subject: `Re: ${thread.subject}`,
          message: replyMessage,
          parentid: thread.id,
          threadid: thread.threadid,
          senderid: currentSender.id,
          receiverid: currentReceiver.id,
          messagetype: 'reply',
          contenttype: 'text',
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          status: thread.status,
          isdelivered: true,
          deliveredat: faker.date.past(),
          createdat: new Date(thread.createdat.getTime() + (i + 1) * 60000 * 30), // 30 minutes apart
        },
      })

      chatReplies.push(reply)
    }
  }

  // Create some chat messages with attachments
  console.log('📎 Creating chat messages with attachments...')

  const attachmentMessages = await Promise.all(
    Array.from({ length: 4 }, () => {
      const sender = randomChoice(allChatUsers)
      const receiver = randomChoice(allChatUsers.filter(u => u.id !== sender.id))
      const threadId = BigInt(Date.now() + Math.floor(Math.random() * 1000))

      const attachments = JSON.stringify([
        {
          id: faker.string.uuid(),
          name: `document_${faker.number.int({ min: 1, max: 999 })}.pdf`,
          type: randomChoice(['image/jpeg', 'application/pdf', 'text/plain', 'application/docx']),
          size: faker.number.int({ min: 1024, max: 5242880 }), // 1KB to 5MB
          url: faker.internet.url(),
          uploadedAt: new Date().toISOString()
        }
      ])

      return prisma.contactforms.create({
        data: {
          name: sender.firstname + ' ' + (sender.lastname || ''),
          email: sender.email,
          phone: faker.phone.number(),
          subject: 'File Sharing',
          message: randomChoice([
            'I\'ve attached the document you requested. Please review and let me know your thoughts.',
            'Here\'s the file we discussed. Looking forward to your feedback!',
            'Attached is the latest version. Please check if this meets your requirements.',
            'I\'m sharing this file for your review. Let me know if you need any changes.'
          ]),
          senderid: sender.id,
          receiverid: receiver.id,
          threadid: threadId,
          messagetype: 'chat',
          contenttype: 'text',
          attachments: attachments,
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          status: randomChoice(['New', 'In Progress']),
          isdelivered: true,
          deliveredat: faker.date.past(),
          createdat: faker.date.past({ days: 7 }),
        },
      })
    })
  )

  // Create some messages with rich content and metadata
  console.log('🎨 Creating rich content messages...')

  const richContentMessages = await Promise.all(
    Array.from({ length: 3 }, () => {
      const sender = randomChoice(allChatUsers)
      const receiver = randomChoice(allChatUsers.filter(u => u.id !== sender.id))
      const threadId = BigInt(Date.now() + Math.floor(Math.random() * 1000))

      const metadata = JSON.stringify({
        priority: randomChoice(['low', 'normal', 'high']),
        tags: randomChoices(['urgent', 'follow-up', 'question', 'feedback'], 2),
        readReceipt: faker.datatype.boolean(),
        deliveryStatus: 'delivered',
        messageId: faker.string.uuid(),
      })

      const editHistory = JSON.stringify([
        {
          timestamp: new Date().toISOString(),
          content: 'Original message content',
          editedBy: sender.id.toString(),
        }
      ])

      return prisma.contactforms.create({
        data: {
          name: sender.firstname + ' ' + (sender.lastname || ''),
          email: sender.email,
          phone: faker.phone.number(),
          subject: randomChoice([
            'Project Update',
            'Important Announcement',
            'Meeting Schedule',
            'Document Review'
          ]),
          message: randomChoice([
            'I wanted to give you an update on the project progress. We\'ve completed the initial phase and are ready to move to the next stage.',
            'Please review the attached documents and let me know if you have any questions or concerns.',
            'I\'d like to schedule a meeting to discuss the project requirements in more detail. When would be a good time for you?',
            'The latest version is ready for testing. Could you please review and provide your feedback?'
          ]),
          senderid: sender.id,
          receiverid: receiver.id,
          threadid: threadId,
          messagetype: 'chat',
          contenttype: randomChoice(['text', 'html']),
          metadata: metadata,
          edithistory: editHistory,
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          status: randomChoice(['New', 'In Progress']),
          isdelivered: true,
          deliveredat: faker.date.past(),
          createdat: faker.date.past({ days: 3 }),
        },
      })
    })
  )

  // Create some unread messages for testing notification systems
  console.log('🔔 Creating unread messages for notifications...')

  const unreadMessages = await Promise.all(
    Array.from({ length: 5 }, () => {
      const sender = randomChoice(clientUsers)
      const receiver = randomChoice(adminUsers)
      const threadId = BigInt(Date.now() + Math.floor(Math.random() * 1000))

      return prisma.contactforms.create({
        data: {
          name: sender.firstname + ' ' + (sender.lastname || ''),
          email: sender.email,
          phone: faker.phone.number(),
          subject: randomChoice([
            'Urgent: Need Help',
            'Quick Question',
            'Support Request',
            'New Message'
          ]),
          message: randomChoice([
            'Hi! I need some urgent help with my project. Could you please get back to me as soon as possible?',
            'Quick question about the billing. Could you clarify this for me?',
            'I\'m having some technical issues. Could someone help me troubleshoot?',
            'Just wanted to check in on the project status. Any updates?'
          ]),
          senderid: sender.id,
          receiverid: receiver.id,
          threadid: threadId,
          messagetype: 'chat',
          contenttype: 'text',
          isread: false, // Explicitly unread for testing
          readat: null,
          status: 'New',
          isdelivered: true,
          deliveredat: faker.date.recent(),
          createdat: faker.date.recent(),
        },
      })
    })
  )

  console.log(`✅ Created ${contactForms.length} contact forms, ${chatThreads.length} chat threads, ${chatReplies.length} chat replies, ${attachmentMessages.length} attachment messages, ${richContentMessages.length} rich content messages, and ${unreadMessages.length} unread messages`)

  // Skip hero sections and about pages - not in current schema

  // 22. Create Legal Pages (no dependencies)
  console.log('⚖️ Creating legal pages...')
  const legalPages = await Promise.all([
    prisma.legalpages.create({
      data: {
        title: 'Privacy Policy',
        slug: 'privacy-policy',
        metadescription: 'Our privacy policy explains how we collect and use your data',
        content: faker.lorem.paragraphs(10),
        isactive: true,
        displayorder: 1,
        lastmodified: new Date(),
        modifiedby: 'admin',
      },
    }),
    prisma.legalpages.create({
      data: {
        title: 'Terms of Service',
        slug: 'terms-of-service',
        metadescription: 'Terms and conditions for using our services',
        content: faker.lorem.paragraphs(8),
        isactive: true,
        displayorder: 2,
        lastmodified: new Date(),
        modifiedby: 'admin',
      },
    }),
  ])

  // 23. Create Legal Page Sections (depends on legal pages)
  console.log('📋 Creating legal page sections...')
  await Promise.all(
    legalPages.flatMap(page =>
      Array.from({ length: faker.number.int({ min: 3, max: 6 }) }, (_, index) =>
        prisma.legalpagesections.create({
          data: {
            legalpageid: page.id,
            title: faker.lorem.sentence(),
            content: faker.lorem.paragraphs(3),
            iconclass: `fas fa-${randomChoice(['gavel', 'shield', 'lock', 'eye', 'file'])}`,
            displayorder: index,
            isactive: true,
          },
        })
      )
    )
  )

  // 24. Create Site Settings (no dependencies)
  console.log('⚙️ Creating site settings...')
  const siteSettingsData = [
    { key: 'site_name', value: 'Technoloway', category: 'GENERAL', description: 'Website name' },
    { key: 'site_description', value: 'Leading software development company', category: 'GENERAL', description: 'Website description' },
    { key: 'contact_email', value: '<EMAIL>', category: 'CONTACT', description: 'Main contact email' },
    { key: 'contact_phone', value: '+****************', category: 'CONTACT', description: 'Main contact phone' },
    { key: 'company_address', value: '123 Tech Street, Silicon Valley, CA 94000', category: 'CONTACT', description: 'Company address' },
    { key: 'social_facebook', value: 'https://facebook.com/technoloway', category: 'SOCIAL', description: 'Facebook URL' },
    { key: 'social_twitter', value: 'https://twitter.com/technoloway', category: 'SOCIAL', description: 'Twitter URL' },
    { key: 'social_linkedin', value: 'https://linkedin.com/company/technoloway', category: 'SOCIAL', description: 'LinkedIn URL' },
    { key: 'social_github', value: 'https://github.com/technoloway', category: 'SOCIAL', description: 'GitHub URL' },
    { key: 'business_hours', value: 'Monday - Friday: 9:00 AM - 6:00 PM', category: 'GENERAL', description: 'Business hours' },
  ]

  await Promise.all(
    siteSettingsData.map(setting =>
      prisma.sitesettings.create({
        data: {
          key: setting.key,
          value: setting.value,
          category: setting.category,
          description: setting.description,
          ispublic: true,
          isactive: true,
        },
      })
    )
  )

  // 25. Create additional entities for completeness
  console.log('🔧 Creating additional entities...')

  // Create Tasks (depends on projects and team members)
  await Promise.all(
    projects.flatMap(project =>
      Array.from({ length: faker.number.int({ min: 2, max: 6 }) }, () =>
        prisma.tasks.create({
          data: {
            projno: project.id,
            teammemberid: randomChoice(teamMembers).id,
            taskdesc: faker.lorem.sentence(),
            taskstartdate: faker.date.past(),
            taskenddate: faker.date.future(),
            workhours: faker.number.int({ min: 8, max: 40 }),
            payrate: faker.number.float({ min: 25, max: 150, fractionDigits: 2 }),
            status: randomChoice(['Active', 'Completed', 'On Hold']),
            notes: faker.lorem.sentence(),
          },
        })
      )
    )
  )

  // Create Feedbacks (depends on clients and projects)
  await Promise.all(
    Array.from({ length: 8 }, () =>
      prisma.feedbacks.create({
        data: {
          subject: faker.lorem.sentence(),
          comment: faker.lorem.paragraph(),
          feedbacktype: randomChoice(['Bug Report', 'Feature Request', 'General', 'Complaint']),
          rating: faker.number.int({ min: 1, max: 5 }),
          clientname: faker.person.fullName(),
          clientemail: faker.internet.email(),
          priority: randomChoice(['Low', 'Medium', 'High', 'Critical']),
          status: randomChoice(['New', 'In Progress', 'Resolved', 'Closed']),
          isread: faker.datatype.boolean(),
          readat: faker.datatype.boolean() ? faker.date.past() : null,
          resolvedat: faker.datatype.boolean() ? faker.date.past() : null,
          adminresponse: faker.datatype.boolean() ? faker.lorem.paragraph() : null,
          adminname: faker.datatype.boolean() ? faker.person.fullName() : null,
          responsedate: faker.datatype.boolean() ? faker.date.past() : null,
          clientid: randomChoice([null, ...clients.map(c => c.id)]),
          projectid: randomChoice([null, ...projects.map(p => p.id)]),
          ispublic: faker.datatype.boolean(),
        },
      })
    )
  )

  // Create Messages (depends on projects)
  await Promise.all(
    projects.flatMap(project =>
      Array.from({ length: faker.number.int({ min: 3, max: 8 }) }, () =>
        prisma.messages.create({
          data: {
            content: faker.lorem.paragraph(),
            sendername: faker.person.fullName(),
            senderrole: randomChoice(['CLIENT', 'ADMIN', 'DEVELOPER']),
            senderid: faker.string.uuid(),
            isread: faker.datatype.boolean(),
            readat: faker.datatype.boolean() ? faker.date.past() : null,
            projectid: project.id,
          },
        })
      )
    )
  )

  console.log('✅ All entities created successfully')
  console.log('🎉 Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
